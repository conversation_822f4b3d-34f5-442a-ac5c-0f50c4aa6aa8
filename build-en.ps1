# Windows Build Script for Examinees Collection System

Write-Host "=== Building Examinees Collection System ===" -ForegroundColor Green
Write-Host ""

# Build TypeScript
Write-Host "Building TypeScript project..." -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed" -ForegroundColor Red
    exit 1
}

# Fix JSON imports
Write-Host "Fixing JSON imports..." -ForegroundColor Yellow
$indexPath = "dist\utils\index.js"
if (Test-Path $indexPath) {
    $content = Get-Content $indexPath -Raw
    $content = $content -replace 'import province from "\.\.\/constans\/province\.json";', 'import province from "../constans/province.json" with { type: "json" };'
    $content = $content -replace 'import license from "\.\.\/constans\/licenseName\.json";', 'import license from "../constans/licenseName.json" with { type: "json" };'
    Set-Content $indexPath $content -Encoding UTF8
    Write-Host "JSON imports fixed successfully" -ForegroundColor Green
} else {
    Write-Host "Warning: index.js not found" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Usage:" -ForegroundColor Cyan
Write-Host "  npm run start" -ForegroundColor White
Write-Host "  or: node dist/index.js" -ForegroundColor White
Write-Host ""
