# 科目8（全科目）与科目9（科目二三）采集可行性分析

本文评估在现有代码基础上，新增“科目二三”（代码 9）与“全科目”（代码 8）采集的可行性、影响范围与改造建议。

—

一、现状回顾（kskm 的使用与约束）
1) 主流程仅循环 1..4 科目：在 collectAllExamData 中固定 for (1..4) 拉取考场→场次→考生并入库。
2) API 层按传入 kskm 透传：
   - getExamCenterOptionsList 将 kskm 放入 JSON 参数调用 /netdrv/pub/getExamSite；
   - getExamSessionList 将 kskm、ksdd、日期等构造成 body；仅当 kskm === "1" 时额外附加 jhlx、ywlx；未限制 1..4 以外的值。
3) 入库 exam_subject 来源：当前入库 exam_subject 取自“循环传入的 kskm”，并非来源于场次对象。
4) 统计逻辑固定围绕 1/2/3/4：所有聚合查询均以 exam_subject in {'1','2','3','4'} 作为维度过滤与拆分。

—

二、8/9 的语义与影响
- 代码 8（全科目）：若后端支持，可能返回“覆盖所有科目”的考场与场次集合，场次对象通常包含实际科目字段（如 session.kskm）。
- 代码 9（科目二三）：若后端支持，可能返回仅包含科目 2、3 的集合，场次对象同样应自带实际科目。
- 关键影响：若仍用“循环传入的 kskm”入库，则 8/9 入口采集的数据会被标记为 '8' 或 '9'，统计模块（只识别 1/2/3/4）将无法覆盖这些数据。

—

三、可行性评估
- API 层：当前实现对 kskm 基本透传，除 kskm==="1" 有额外字段外，对 8/9 并无显式限制，具备尝试空间。
- 业务语义：需在入库时“按实际科目归一至 1/2/3/4”，否则统计不可见。
- 重复风险：若同时启用 1..4 与 8/9，极可能重复抓取同一场次/考生；现有表无唯一约束，重复会沉淀。
- 统计适配：若入库归一为 1/2/3/4，统计可无改动继续使用。

—

四、最小改动方案（推荐）
1) 入库科目归一：在保存记录时，exam_subject 优先取场次的实际科目（session.kskm?.toString()），若不存在再退回外层传入的 kskm。这样即使循环传入 8/9，也能将每条记录按真实 1/2/3/4 写入。
2) 采集科目集合配置化：将固定 1..4 的循环改为读取配置 subjects（字符串数组，默认 ["1","2","3","4"]），并在解析配置后做“互斥收敛”：
   - 若包含 "8"（全科目），忽略 "1".."4"（仅保留 "8"），避免重复。
   - 若包含 "9"（科目二三），忽略 "2"、"3"（仅保留 "9"），避免重复。
3) 统计逻辑保持不变：因已入库归一到 1..4。

—

五、风险与对策
- 后端是否支持 8/9：若不支持，会返回错误或空数据。对策：先小范围探索性请求并打印结构，确认字段兼容性（尤其 session.kskm）。
- 返回结构差异：8/9 的场次或考生结构可能与 1..4 不同。对策：在处理前增加存在性检查与默认值，异常跳过并记录日志。
- 重复数据：误将 8 与 1..4 或 9 与 2/3 同时启用会重复。对策：配置解析时互斥收敛；中期建议为表加唯一约束并使用 UPSERT。
- 性能与 I/O：全科目可能显著放大数据量。对策：采用批量/阈值落盘策略以降低 I/O 开销。

—

六、实施步骤（按优先级）
1) 入库改造：在写入前，将 exam_subject 设置为 session.kskm?.toString() || kskm。
2) 配置化科目集合：
   - 在配置中新增 subjects（支持环境变量 SUBJECTS=逗号分隔）；
   - 解析后做互斥收敛；
   - 采集主循环遍历 CONFIG.subjects，而非固定 1..4。
3) 临时调试日志：对 kskm 为 "8"/"9" 的接口响应做结构样例打印，确认兼容后再关闭。
4) 小范围试跑：限定少量考场/短日期窗口试运行，验证入库与统计结果。

—

七、测试要点
- 方案 A：subjects=["8"]，短窗口试跑，验证：
  - 能获取考场/场次；session.kskm 存在；
  - 入库 exam_subject 均为 1/2/3/4；
  - 统计页可反映新增数据。
- 方案 B：subjects=["9"]，验证仅产生科目 2、3 的数据，且统计可见。
- 互斥验证：
  - subjects 包含 ["8","1"] 时，最终仅执行 "8"；
  - subjects 包含 ["9","2","3"] 时，最终仅执行 "9"。
- 回归：恢复默认 ["1","2","3","4"] 行为一致。

—

八、结论
- 技术可行性：高。API 层无需大改；关键在“入库科目归一”和“采集集合配置化+互斥收敛”。
- 主要风险：后台支持性、结构差异、重复采集、性能与 I/O。
- 建议路径：按最小改动方案先行试点，稳定后再考虑引入唯一约束/UPSERT 与批量落盘优化。