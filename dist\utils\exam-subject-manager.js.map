{"version": 3, "file": "exam-subject-manager.js", "sourceRoot": "", "sources": ["../../src/utils/exam-subject-manager.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,WAAW,EACX,WAAW,EACX,eAAe,EACf,qBAAqB,EACrB,oBAAoB,EACpB,yBAAyB,EACzB,2BAA2B,EAC3B,kBAAkB,EAClB,yBAAyB,EACzB,gCAAgC,EAChC,mBAAmB,EACnB,kBAAkB,EACnB,MAAM,0BAA0B,CAAC;AAElC;;;GAGG;AACH,MAAM,OAAO,kBAAkB;IAC7B;;OAEG;IACH,MAAM,CAAC,2BAA2B,CAChC,cAAwB,EACxB,mBAAkC;QAElC,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAe,CAAC;QAEnD,gBAAgB;QAChB,mBAAmB,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YACxC,MAAM,QAAQ,GAAG,yBAAyB,CAAC,WAAW,CAAC,CAAC;YACxD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,kBAAkB;QAClB,OAAO,cAAc;aAClB,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,OAAsB,CAAC,CAAC;aAC9E,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAsB,CAAC;aACtC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,uBAAuB,CAAC,QAAuB;QACpD,MAAM,MAAM,GAA2C;YACrD,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YACzB,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,EAAE;SACjC,CAAC;QAEF,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,iBAAiB;YACjB,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5D,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5C,CAAC;YACD,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;gBACnE,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,OAAoB,EAAE,QAAyB;QACzE,MAAM,YAAY,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QACzD,OAAO,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CACrC,2BAA2B,CAAC,WAAW,EAAE,OAAO,CAAC,CAClD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,WAAmB,EAAE,WAAmB;QAMlE,QAAQ;QACR,MAAM,qBAAqB,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACrE,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC3B,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,UAAU,WAAW,EAAE;aAC/B,CAAC;QACJ,CAAC;QAED,QAAQ;QACR,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,UAAU,WAAW,EAAE;aAC/B,CAAC;QACJ,CAAC;QAED,WAAW;QACX,MAAM,OAAO,GAAG,gCAAgC,CAAC,iBAAiB,EAAE,qBAAqB,CAAC,CAAC;QAC3F,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,MAAM,kBAAkB,CAAC,qBAAqB,CAAC,SAAS,mBAAmB,CAAC,iBAAiB,CAAC,EAAE;aACxG,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,qBAAqB;YACrB,iBAAiB;SAClB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,cAAsB;QAChD,IAAI,CAAC,cAAc;YAAE,OAAO,IAAI,CAAC;QAEjC,MAAM,QAAQ,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;QAErD,OAAO;QACP,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,QAAuB,CAAC,EAAE,CAAC;YACjE,OAAO,QAAuB,CAAC;QACjC,CAAC;QAED,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/E,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACzD,aAAa;YACb,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,IAAmB,CAAC,EAAE,CAAC;oBAC7D,OAAO,IAAmB,CAAC;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;QACP,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;YACrD,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACnC,OAAO,WAAW,CAAC;YACrB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,UAAkB;QACxC,IAAI,CAAC,UAAU;YAAE,OAAO,IAAI,CAAC;QAE7B,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;QAElC,SAAS;QACT,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,OAAsB,CAAC,EAAE,CAAC;YAChE,OAAO,OAAsB,CAAC;QAChC,CAAC;QAED,OAAO;QACP,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,MAAqB,CAAC,EAAE,CAAC;gBAC/D,OAAO,MAAqB,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,0BAA0B,CAAC,cAAwB;QAKxD,MAAM,gBAAgB,GAAkB,EAAE,CAAC;QAC3C,MAAM,gBAAgB,GAAa,EAAE,CAAC;QAEtC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,OAAsB,CAAC,EAAE,CAAC;gBAChE,gBAAgB,CAAC,IAAI,CAAC,OAAsB,CAAC,CAAC;YAChD,CAAC;iBAAM,CAAC;gBACN,aAAa;gBACb,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,gBAAgB;YAChB,mBAAmB,EAAE,gBAAgB,CAAC,MAAM,GAAG,CAAC;YAChD,gBAAgB;SACjB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,wBAAwB,CAC7B,eAAuB,EACvB,WAAgB;QAEhB,eAAe;QACf,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC;YACpC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACzE,IAAI,aAAa,EAAE,CAAC;gBAClB,OAAO,aAAa,CAAC;YACvB,CAAC;QACH,CAAC;QAED,gBAAgB;QAChB,mBAAmB;QACnB,QAAQ,eAAe,EAAE,CAAC;YACxB,KAAK,GAAG;gBACN,0BAA0B;gBAC1B,OAAO,WAAW,CAAC,SAAS,CAAC;YAC/B,KAAK,GAAG;gBACN,0BAA0B;gBAC1B,OAAO,WAAW,CAAC,SAAS,CAAC;YAC/B;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,QAAe;QAKvC,MAAM,UAAU,GAAyD;YACvE,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE;YACzB,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,EAAE;SACjC,CAAC;QAEF,MAAM,aAAa,GAAqD,EAAS,CAAC;QAClF,MAAM,cAAc,GAAU,EAAE,CAAC;QAEjC,UAAU;QACV,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YAC/C,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,yBAAyB,CAAC,WAAW,CAAC,CAAC;YACxD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACzB,aAAa,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC3C,UAAU,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC7C,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACxB,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAC1C,MAAM,CAAC,aAAa,EACpB,MAAM,CAAC,YAAY,CACpB,CAAC;YAEF,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,qBAAqB,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;gBAC9F,cAAc,CAAC,IAAI,CAAC;oBAClB,GAAG,MAAM;oBACT,KAAK,EAAE,UAAU,CAAC,KAAK;iBACxB,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,MAAM,WAAW,GAAG,UAAU,CAAC,qBAAqB,CAAC;YACrD,MAAM,OAAO,GAAG,UAAU,CAAC,iBAAiB,CAAC;YAC7C,MAAM,QAAQ,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;YAEjD,OAAO;YACP,aAAa,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;YACtC,UAAU,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,UAAU;YACV,aAAa;YACb,cAAc;SACf,CAAC;IACJ,CAAC;CACF;AAED,eAAe,kBAAkB,CAAC"}