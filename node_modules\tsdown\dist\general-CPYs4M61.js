//#region src/utils/general.ts
function toArray(val, defaultValue) {
	if (Array.isArray(val)) return val;
	else if (val == null) {
		if (defaultValue) return [defaultValue];
		return [];
	} else return [val];
}
function resolveComma(arr) {
	return arr.flatMap((format) => format.split(","));
}
function debounce(fn, wait) {
	let timeout;
	return function(...args) {
		if (timeout) clearTimeout(timeout);
		timeout = setTimeout(() => {
			timeout = void 0;
			fn.apply(this, args);
		}, wait);
	};
}
function slash(string) {
	return string.replaceAll("\\", "/");
}
const noop = (v) => v;

//#endregion
export { debounce, noop, resolveComma, slash, toArray };