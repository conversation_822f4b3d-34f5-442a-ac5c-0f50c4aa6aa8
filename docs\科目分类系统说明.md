# 驾驶证考试科目分类系统

## 概述

本系统实现了清晰的驾驶证考试科目分类，明确区分不同车型的科目设置，避免数据重复问题。

## 车型与科目对应关系

### 小型汽车类（C1、C2、C5）
- **科目一**：道路交通安全法律、法规和相关知识考试
- **科目二**：场地驾驶技能考试  
- **科目三**：道路驾驶技能考试
- **科目四**：安全文明驾驶常识考试

### 摩托车类（D、E、F）
- **科目二**：场地驾驶技能考试
- **科目三**：道路驾驶技能考试

**注意**：摩托车类不需要考科目一和科目四，因此不存在数据重复问题。

## 核心文件

### 1. 类型定义 (`src/types/exam-subject.ts`)
```typescript
// 车型枚举
export enum VehicleType {
  // 小型汽车类
  C1 = "C1", C2 = "C2", C5 = "C5",
  // 摩托车类  
  D = "D", E = "E", F = "F"
}

// 科目枚举
export enum ExamSubject {
  SUBJECT_1 = "1", SUBJECT_2 = "2", 
  SUBJECT_3 = "3", SUBJECT_4 = "4"
}
```

### 2. 科目管理器 (`src/utils/exam-subject-manager.ts`)
```typescript
// 获取指定车型的考试科目
ExamSubjectManager.getExamSubjectsForVehicle(VehicleType.C1)
// 返回: [ExamSubject.SUBJECT_1, ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3, ExamSubject.SUBJECT_4]

ExamSubjectManager.getExamSubjectsForVehicle(VehicleType.D)  
// 返回: [ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3]

// 验证车型和科目组合
ExamSubjectManager.validateExamineeData("C1", "1") // 有效
ExamSubjectManager.validateExamineeData("D", "1")  // 无效
```

### 3. 配置加载 (`src/utils/loadConfig.ts`)
支持灵活的车型和科目配置，自动处理兼容性验证。

## 配置示例

### 小型汽车考场配置
```json
{
  "subjects": ["1", "2", "3", "4"],
  "enable_car_exams": true,
  "enable_motorcycle_exams": false,
  "enabled_vehicle_types": ["C1", "C2", "C5"]
}
```

### 摩托车考场配置  
```json
{
  "subjects": ["2", "3"],
  "enable_car_exams": false,
  "enable_motorcycle_exams": true,
  "enabled_vehicle_types": ["D", "E", "F"]
}
```

### 混合考场配置
```json
{
  "subjects": ["1", "2", "3", "4"],
  "enable_car_exams": true,
  "enable_motorcycle_exams": true,
  "enabled_vehicle_types": ["C1", "C2", "C5", "D", "E", "F"]
}
```

## 特殊科目处理

### 科目8（全科目）
当配置中包含科目8时，系统会：
- 自动忽略科目1-4的配置，避免重复采集
- 在数据处理时根据实际返回的科目进行归类

### 科目9（科目二三）
当配置中包含科目9时，系统会：
- 自动忽略科目2、3的单独配置，避免重复采集
- 在数据处理时将数据正确归类到科目2或科目3

## 使用方法

### 基本验证
```typescript
import ExamSubjectManager from './utils/exam-subject-manager.js';

// 验证考生数据
const result = ExamSubjectManager.validateExamineeData("C1", "1");
if (result.isValid) {
  console.log("数据有效");
  // 使用 result.normalizedVehicleType 和 result.normalizedSubject
} else {
  console.error("数据无效:", result.error);
}
```

### 配置验证
```typescript
import { validateConfiguration } from './enhanced-exam-collection.js';

const config = loadConfig();
if (validateConfiguration(config)) {
  console.log("配置有效");
} else {
  console.error("配置无效");
}
```

### 生成统计报告
```typescript
import { generateEnhancedReport } from './enhanced-exam-collection.js';

await generateEnhancedReport(database);
// 输出按车型类别和具体车型的统计信息
```

## 数据库结构

考生记录中的关键字段：
- `exam_car_type`: 考试车型（使用标准化枚举值）
- `exam_subject`: 考试科目（1-4）
- `allowed_car_type`: 准考车型

系统会自动验证这些字段的组合是否符合实际考试规则。

## 错误处理

系统会捕获并记录以下错误：
1. 无效的车型代码
2. 无效的科目代码  
3. 车型与科目的不兼容组合
4. 配置文件中的无效设置

所有无效记录都会被标记并在统计报告中单独显示。

## 扩展性

系统设计具有良好的扩展性：
- 可以轻松添加新的车型
- 支持新的科目类型
- 配置文件支持灵活的组合