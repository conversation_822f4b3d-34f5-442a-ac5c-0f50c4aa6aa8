{"name": "rolldown", "version": "1.0.0-beta.8-commit.534fde3", "description": "Fast JavaScript/TypeScript bundler in Rust with Rollup-compatible API.", "type": "commonjs", "homepage": "https://rolldown.rs/", "repository": {"type": "git", "url": "git+https://github.com/rolldown/rolldown.git", "directory": "packages/rolldown"}, "license": "MIT", "keywords": ["webpack", "parcel", "esbuild", "rollup", "bundler", "rolldown"], "files": ["bin", "cli", "dist", "!dist/*.node"], "bin": {"rolldown": "./bin/cli.mjs"}, "main": "./dist/index.cjs", "types": "./dist/index.d.cts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.mjs"}, "./experimental": {"require": "./dist/experimental-index.cjs", "import": "./dist/experimental-index.mjs"}, "./filter": {"require": "./dist/filter-index.cjs", "import": "./dist/filter-index.mjs"}, "./parallelPlugin": {"require": "./dist/parallel-plugin.cjs", "import": "./dist/parallel-plugin.mjs"}, "./parseAst": {"require": "./dist/parse-ast-index.cjs", "import": "./dist/parse-ast-index.mjs"}, "./package.json": "./package.json"}, "imports": {"#parallel-plugin-worker": "./dist/parallel-plugin-worker.mjs"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "napi": {"binaryName": "rolldown-binding", "packageName": "@rolldown/binding", "targets": ["x86_64-apple-darwin", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-gnu", "x86_64-unknown-linux-musl", "x86_64-unknown-freebsd", "i686-pc-windows-msvc", "armv7-unknown-linux-gnueabihf", "aarch64-unknown-linux-gnu", "aarch64-apple-darwin", "aarch64-unknown-linux-musl", "aarch64-pc-windows-msvc", "wasm32-wasip1-threads"], "wasm": {"initialMemory": 16384, "browser": {"fs": true, "asyncInit": true}}, "dtsHeader": "type MaybePromise<T> = T | Promise<T>\ntype Nullable<T> = T | null | undefined\ntype VoidNullable<T = void> = T | null | undefined | void\nexport type BindingStringOrRegex = string | RegExp\n\n"}, "dependencies": {"@oxc-project/types": "0.68.1", "ansis": "^3.17.0"}, "peerDependencies": {"@oxc-project/runtime": "0.68.1"}, "peerDependenciesMeta": {"@oxc-project/runtime": {"optional": true}}, "devDependencies": {"@napi-rs/cli": "^3.0.0-alpha.77", "@napi-rs/wasm-runtime": "^0.2.4", "@oxc-node/cli": "^0.0.25", "@oxc-node/core": "^0.0.25", "@types/fs-extra": "^11.0.4", "@types/lodash-es": "^4.17.12", "@valibot/to-json-schema": "1.0.0", "consola": "^3.4.2", "emnapi": "^1.2.0", "execa": "^9.2.0", "fs-extra": "^11.2.0", "glob": "^11.0.0", "locate-character": "^3.0.0", "oxc-parser": "0.68.1", "pathe": "^2.0.3", "remeda": "^2.10.0", "rolldown-plugin-dts": "^0.7.12", "rollup": "^4.18.0", "signal-exit": "4.1.0", "source-map": "^0.7.4", "type-fest": "^4.20.0", "typedoc": "^0.28.0", "typescript": "^5.7.3", "unbuild": "^3.0.0", "valibot": "1.0.0", "@rolldown/testing": "0.0.1", "rolldown": "1.0.0-beta.8-commit.534fde3"}, "optionalDependencies": {"@rolldown/binding-darwin-arm64": "1.0.0-beta.8-commit.534fde3", "@rolldown/binding-darwin-x64": "1.0.0-beta.8-commit.534fde3", "@rolldown/binding-linux-arm-gnueabihf": "1.0.0-beta.8-commit.534fde3", "@rolldown/binding-linux-arm64-gnu": "1.0.0-beta.8-commit.534fde3", "@rolldown/binding-linux-arm64-musl": "1.0.0-beta.8-commit.534fde3", "@rolldown/binding-linux-x64-gnu": "1.0.0-beta.8-commit.534fde3", "@rolldown/binding-freebsd-x64": "1.0.0-beta.8-commit.534fde3", "@rolldown/binding-wasm32-wasi": "1.0.0-beta.8-commit.534fde3", "@rolldown/binding-win32-arm64-msvc": "1.0.0-beta.8-commit.534fde3", "@rolldown/binding-win32-ia32-msvc": "1.0.0-beta.8-commit.534fde3", "@rolldown/binding-linux-x64-musl": "1.0.0-beta.8-commit.534fde3", "@rolldown/binding-win32-x64-msvc": "1.0.0-beta.8-commit.534fde3"}, "scripts": {"# Scrips for binding #": "_", "artifacts": "napi artifacts --cwd ./src --package-json-path ../package.json -o=../artifacts --npm-dir ../npm", "build-binding": "oxnode ./build-binding.mts", "build-binding:release": "pnpm build-binding --release", "build-binding:wasi": "pnpm build-binding --target wasm32-wasip1-threads", "build-binding:wasi:release": "pnpm build-binding --profile release-wasi --target wasm32-wasip1-threads", "# Scrips for node #": "_", "build-node": "oxnode ./build.ts", "build-types-check": "tsc -p ./tsconfig.check.json", "build-js-glue": "pnpm run --sequential '/^build-(types|node|types-check)$/'", "build-native:debug": "pnpm run --sequential '/^build-(binding|js-glue)$/'", "build-native:release": "pnpm run --sequential '/^build-(binding:release|js-glue)$/'", "build-browser:debug": "BROWSER_PKG=1 pnpm run --sequential '/^build-(binding|binding:wasi|node)$/'", "build-browser:release": "BROWSER_PKG=1 pnpm run --sequential '/^build-(binding|binding:wasi:release|node)$/'", "# Scrips for docs #": "_", "extract-options-doc": "typedoc"}}