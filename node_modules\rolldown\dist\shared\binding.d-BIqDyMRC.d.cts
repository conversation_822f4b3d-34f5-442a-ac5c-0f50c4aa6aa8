import { Program } from "@oxc-project/types";

//#region src/binding.d.ts
type MaybePromise<T> = T | Promise<T>
type VoidNullable<T = void> = T | null | undefined | void
type BindingStringOrRegex = string | RegExp
declare class BindingBundleEndEventData {
  output: string
  duration: number
}
declare class BindingError {
  kind: string
  message: string
}
declare class BindingRenderedChunk {
  get name(): string
  get isEntry(): boolean
  get isDynamicEntry(): boolean
  get facadeModuleId(): string | null
  get moduleIds(): Array<string>
  get exports(): Array<string>
  get fileName(): string
  get modules(): BindingModules
  get imports(): Array<string>
  get dynamicImports(): Array<string>
}
declare class BindingRenderedModule {
  get code(): string | null
  get renderedExports(): Array<string>
}
declare class BindingWatcherChangeData {
  path: string
  kind: string
}
declare class BindingWatcherEvent {
  eventKind(): string
  watchChangeData(): BindingWatcherChangeData
  bundleEndData(): BindingBundleEndEventData
  bundleEventKind(): string
  errors(): Array<Error | BindingError>
}
declare class ParseResult {
  get program(): Program
  get module(): EcmaScriptModule
  get comments(): Array<Comment>
  get errors(): Array<OxcError>
}
interface BindingBuildImportAnalysisPluginConfig {
  preloadCode: string
  insertPreload: boolean
  optimizeModulePreloadRelativePaths: boolean
  renderBuiltUrl: boolean
  isRelativeBase: boolean
}
type BindingBuiltinPluginName =  'builtin:wasm-helper'|
'builtin:import-glob'|
'builtin:dynamic-import-vars'|
'builtin:module-preload-polyfill'|
'builtin:manifest'|
'builtin:load-fallback'|
'builtin:transform'|
'builtin:wasm-fallback'|
'builtin:alias'|
'builtin:json'|
'builtin:build-import-analysis'|
'builtin:replace'|
'builtin:vite-resolve'|
'builtin:module-federation'|
'builtin:isolated-declaration'|
'builtin:report';
interface BindingDynamicImportVarsPluginConfig {
  include?: Array<BindingStringOrRegex>
  exclude?: Array<BindingStringOrRegex>
  resolver?: (id: string, importer: string) => MaybePromise<string | undefined>
}
interface BindingGlobImportPluginConfig {
  root?: string
  restoreQueryExtension?: boolean
}
interface BindingHmrBoundaryOutput {
  boundary: string
  acceptedVia: string
}
interface BindingHmrOutput {
  patch: string
  hmrBoundaries: Array<BindingHmrBoundaryOutput>
  fullReload: boolean
  firstInvalidatedBy?: string
  isSelfAccepting: boolean
  fullReloadReason?: string
}
interface BindingHookResolveIdExtraArgs {
  custom?: number
  isEntry: boolean
  kind: 'import' | 'dynamic-import' | 'require-call'
}
interface BindingIsolatedDeclarationPluginConfig {
  stripInternal?: boolean
}
interface BindingJsonPluginConfig {
  minify?: boolean
  namedExports?: boolean
  stringify?: BindingJsonPluginStringify
}
type BindingJsonPluginStringify =
  boolean | string
interface BindingManifestPluginConfig {
  root: string
  outPath: string
}
interface BindingMfManifest {
  filePath?: string
  disableAssetsAnalyze?: boolean
  fileName?: string
}
interface BindingMinifyOptions {
  mangle?: boolean
  compress?: boolean
  removeWhitespace?: boolean
}
interface BindingModuleFederationPluginOption {
  name: string
  filename?: string
  exposes?: Record<string, string>
  remotes?: Array<BindingRemote>
  shared?: Record<string, BindingShared>
  runtimePlugins?: Array<string>
  manifest?: BindingMfManifest
  getPublicPath?: string
}
interface BindingModulePreloadPolyfillPluginConfig {
  skip?: boolean
}
interface BindingModules {
  values: Array<BindingRenderedModule>
  keys: Array<string>
}
interface BindingRemote {
  type?: string
  entry: string
  name: string
  entryGlobalName?: string
  shareScope?: string
}
interface BindingReplacePluginConfig {
  values: Record<string, string>
  delimiters?: [string, string]
  preventAssignment?: boolean
  objectGuards?: boolean
  sourcemap?: boolean
}
interface BindingReportPluginConfig {
  isTty: boolean
}
interface BindingShared {
  version?: string
  shareScope?: string
  singleton?: boolean
  requiredVersion?: string
  strictVersion?: boolean
}
interface BindingTransformHookExtraArgs {
  moduleType: string
}
interface BindingTransformPluginConfig {
  include?: Array<BindingStringOrRegex>
  exclude?: Array<BindingStringOrRegex>
  jsxRefreshInclude?: Array<BindingStringOrRegex>
  jsxRefreshExclude?: Array<BindingStringOrRegex>
  isServerConsumer?: boolean
  runtimeResolveBase?: string
  jsxInject?: string
  transformOptions?: TransformOptions
}
interface BindingViteResolvePluginConfig {
  resolveOptions: BindingViteResolvePluginResolveOptions
  environmentConsumer: string
  environmentName: string
  builtins: Array<BindingStringOrRegex>
  external: true | string[]
  noExternal: true | Array<string | RegExp>
  dedupe: Array<string>
  finalizeBareSpecifier?: (resolvedId: string, rawId: string, importer: string | null | undefined) => VoidNullable<string>
  finalizeOtherSpecifiers?: (resolvedId: string, rawId: string) => VoidNullable<string>
}
interface BindingViteResolvePluginResolveOptions {
  isBuild: boolean
  isProduction: boolean
  asSrc: boolean
  preferRelative: boolean
  isRequire?: boolean
  root: string
  scan: boolean
  mainFields: Array<string>
  conditions: Array<string>
  externalConditions: Array<string>
  extensions: Array<string>
  tryIndex: boolean
  tryPrefix?: string
  preserveSymlinks: boolean
}
interface Comment {
  type: 'Line' | 'Block'
  value: string
  start: number
  end: number
}
interface CompilerAssumptions {
  ignoreFunctionLength?: boolean
  noDocumentAll?: boolean
  objectRestNoSymbols?: boolean
  pureGetters?: boolean
  /**
   * When using public class fields, assume that they don't shadow any getter in the current class,
   * in its subclasses or in its superclass. Thus, it's safe to assign them rather than using
   * `Object.defineProperty`.
   *
   * For example:
   *
   * Input:
   * ```js
   * class Test {
   *  field = 2;
   *
   *  static staticField = 3;
   * }
   * ```
   *
   * When `set_public_class_fields` is `true`, the output will be:
   * ```js
   * class Test {
   *  constructor() {
   *    this.field = 2;
   *  }
   * }
   * Test.staticField = 3;
   * ```
   *
   * Otherwise, the output will be:
   * ```js
   * import _defineProperty from "@oxc-project/runtime/helpers/defineProperty";
   * class Test {
   *   constructor() {
   *     _defineProperty(this, "field", 2);
   *   }
   * }
   * _defineProperty(Test, "staticField", 3);
   * ```
   *
   * NOTE: For TypeScript, if you wanted behavior is equivalent to `useDefineForClassFields: false`, you should
   * set both `set_public_class_fields` and [`crate::TypeScriptOptions::remove_class_fields_without_initializer`]
   * to `true`.
   */
  setPublicClassFields?: boolean
}
interface DecoratorOptions {
  /**
   * Enables experimental support for decorators, which is a version of decorators that predates the TC39 standardization process.
   *
   * Decorators are a language feature which hasn’t yet been fully ratified into the JavaScript specification.
   * This means that the implementation version in TypeScript may differ from the implementation in JavaScript when it it decided by TC39.
   *
   * @see https://www.typescriptlang.org/tsconfig/#experimentalDecorators
   * @default false
   */
  legacy?: boolean
  /**
   * Enables emitting decorator metadata.
   *
   * This option the same as [emitDecoratorMetadata](https://www.typescriptlang.org/tsconfig/#emitDecoratorMetadata)
   * in TypeScript, and it only works when `legacy` is true.
   *
   * @see https://www.typescriptlang.org/tsconfig/#emitDecoratorMetadata
   * @default false
   */
  emitDecoratorMetadata?: boolean
}
interface DynamicImport {
  start: number
  end: number
  moduleRequest: Span
}
interface EcmaScriptModule {
  /**
   * Has ESM syntax.
   *
   * i.e. `import` and `export` statements, and `import.meta`.
   *
   * Dynamic imports `import('foo')` are ignored since they can be used in non-ESM files.
   */
  hasModuleSyntax: boolean
  /** Import statements. */
  staticImports: Array<StaticImport>
  /** Export statements. */
  staticExports: Array<StaticExport>
  /** Dynamic import expressions. */
  dynamicImports: Array<DynamicImport>
  /** Span positions` of `import.meta` */
  importMetas: Array<Span>
}
interface ErrorLabel {
  message?: string
  start: number
  end: number
}
interface ExportExportName {
  kind: ExportExportNameKind
  name?: string
  start?: number
  end?: number
}
type ExportExportNameKind = /** `export { name } */
'Name'|
/** `export default expression` */
'Default'|
/** `export * from "mod" */
'None';
interface ExportImportName {
  kind: ExportImportNameKind
  name?: string
  start?: number
  end?: number
}
type ExportImportNameKind = /** `export { name } */
'Name'|
/** `export * as ns from "mod"` */
'All'|
/** `export * from "mod"` */
'AllButDefault'|
/** Does not have a specifier. */
'None';
interface ExportLocalName {
  kind: ExportLocalNameKind
  name?: string
  start?: number
  end?: number
}
type ExportLocalNameKind = /** `export { name } */
'Name'|
/** `export default expression` */
'Default'|
/**
 * If the exported value is not locally accessible from within the module.
 * `export default function () {}`
 */
'None';
type HelperMode = /**
 * Runtime mode (default): Helper functions are imported from a runtime package.
 *
 * Example:
 *
 * ```js
 * import helperName from "@oxc-project/runtime/helpers/helperName";
 * helperName(...arguments);
 * ```
 */
'Runtime'|
/**
 * External mode: Helper functions are accessed from a global `babelHelpers` object.
 *
 * Example:
 *
 * ```js
 * babelHelpers.helperName(...arguments);
 * ```
 */
'External';
interface Helpers {
  mode?: HelperMode
}
interface ImportName {
  kind: ImportNameKind
  name?: string
  start?: number
  end?: number
}
type ImportNameKind = /** `import { x } from "mod"` */
'Name'|
/** `import * as ns from "mod"` */
'NamespaceObject'|
/** `import defaultExport from "mod"` */
'Default';
/** TypeScript Isolated Declarations for Standalone DTS Emit */
/** TypeScript Isolated Declarations for Standalone DTS Emit */
declare function isolatedDeclaration(filename: string, sourceText: string, options?: IsolatedDeclarationsOptions | undefined | null): IsolatedDeclarationsResult
interface IsolatedDeclarationsOptions {
  /**
   * Do not emit declarations for code that has an @internal annotation in its JSDoc comment.
   * This is an internal compiler option; use at your own risk, because the compiler does not check that the result is valid.
   *
   * Default: `false`
   *
   * See <https://www.typescriptlang.org/tsconfig/#stripInternal>
   */
  stripInternal?: boolean
  sourcemap?: boolean
}
interface IsolatedDeclarationsResult {
  code: string
  map?: SourceMap
  errors: Array<OxcError>
}
/**
* Configure how TSX and JSX are transformed.
*
* @see {@link https://babeljs.io/docs/babel-plugin-transform-react-jsx#options}
*/
/**
 * Configure how TSX and JSX are transformed.
 *
 * @see {@link https://babeljs.io/docs/babel-plugin-transform-react-jsx#options}
 */
interface JsxOptions {
  /**
   * Decides which runtime to use.
   *
   * - 'automatic' - auto-import the correct JSX factories
   * - 'classic' - no auto-import
   *
   * @default 'automatic'
   */
  runtime?: 'classic' | 'automatic'
  /**
   * Emit development-specific information, such as `__source` and `__self`.
   *
   * @default false
   *
   * @see {@link https://babeljs.io/docs/babel-plugin-transform-react-jsx-development}
   */
  development?: boolean
  /**
   * Toggles whether or not to throw an error if an XML namespaced tag name
   * is used.
   *
   * Though the JSX spec allows this, it is disabled by default since React's
   * JSX does not currently have support for it.
   *
   * @default true
   */
  throwIfNamespace?: boolean
  /**
   * Enables `@babel/plugin-transform-react-pure-annotations`.
   *
   * It will mark top-level React method calls as pure for tree shaking.
   *
   * @see {@link https://babeljs.io/docs/en/babel-plugin-transform-react-pure-annotations}
   *
   * @default true
   */
  pure?: boolean
  /**
   * Replaces the import source when importing functions.
   *
   * @default 'react'
   */
  importSource?: string
  /**
   * Replace the function used when compiling JSX expressions. It should be a
   * qualified name (e.g. `React.createElement`) or an identifier (e.g.
   * `createElement`).
   *
   * Only used for `classic` {@link runtime}.
   *
   * @default 'React.createElement'
   */
  pragma?: string
  /**
   * Replace the component used when compiling JSX fragments. It should be a
   * valid JSX tag name.
   *
   * Only used for `classic` {@link runtime}.
   *
   * @default 'React.Fragment'
   */
  pragmaFrag?: string
  /**
   * When spreading props, use `Object.assign` directly instead of an extend helper.
   *
   * Only used for `classic` {@link runtime}.
   *
   * @default false
   */
  useBuiltIns?: boolean
  /**
   * When spreading props, use inline object with spread elements directly
   * instead of an extend helper or Object.assign.
   *
   * Only used for `classic` {@link runtime}.
   *
   * @default false
   */
  useSpread?: boolean
  /**
   * Enable React Fast Refresh .
   *
   * Conforms to the implementation in {@link https://github.com/facebook/react/tree/v18.3.1/packages/react-refresh}
   *
   * @default false
   */
  refresh?: boolean | ReactRefreshOptions
}
/**
* Transform JavaScript code to a Vite Node runnable module.
*
* @param filename The name of the file being transformed.
* @param sourceText the source code itself
* @param options The options for the transformation. See {@link
* ModuleRunnerTransformOptions} for more information.
*
* @returns an object containing the transformed code, source maps, and any
* errors that occurred during parsing or transformation.
*
* @deprecated Only works for Vite.
*/
/**
 * Transform JavaScript code to a Vite Node runnable module.
 *
 * @param filename The name of the file being transformed.
 * @param sourceText the source code itself
 * @param options The options for the transformation. See {@link
 * ModuleRunnerTransformOptions} for more information.
 *
 * @returns an object containing the transformed code, source maps, and any
 * errors that occurred during parsing or transformation.
 *
 * @deprecated Only works for Vite.
 */
declare function moduleRunnerTransform(filename: string, sourceText: string, options?: ModuleRunnerTransformOptions | undefined | null): ModuleRunnerTransformResult
interface ModuleRunnerTransformOptions {
  /**
   * Enable source map generation.
   *
   * When `true`, the `sourceMap` field of transform result objects will be populated.
   *
   * @default false
   *
   * @see {@link SourceMap}
   */
  sourcemap?: boolean
}
interface ModuleRunnerTransformResult {
  /**
   * The transformed code.
   *
   * If parsing failed, this will be an empty string.
   */
  code: string
  /**
   * The source map for the transformed code.
   *
   * This will be set if {@link TransformOptions#sourcemap} is `true`.
   */
  map?: SourceMap
  deps: Array<string>
  dynamicDeps: Array<string>
  /**
   * Parse and transformation errors.
   *
   * Oxc's parser recovers from common syntax errors, meaning that
   * transformed code may still be available even if there are errors in this
   * list.
   */
  errors: Array<OxcError>
}
interface OxcError {
  severity: Severity
  message: string
  labels: Array<ErrorLabel>
  helpMessage?: string
  codeframe?: string
}
interface ParserOptions {
  sourceType?: 'script' | 'module' | 'unambiguous' | undefined
  /** Treat the source text as `js`, `jsx`, `ts`, or `tsx`. */
  lang?: 'js' | 'jsx' | 'ts' | 'tsx'
  /**
   * Return an AST which includes TypeScript-related properties, or excludes them.
   *
   * `'js'` is default for JS / JSX files.
   * `'ts'` is default for TS / TSX files.
   * The type of the file is determined from `lang` option, or extension of provided `filename`.
   */
  astType?: 'js' | 'ts'
  /**
   * Emit `ParenthesizedExpression` and `TSParenthesizedType` in AST.
   *
   * If this option is true, parenthesized expressions are represented by
   * (non-standard) `ParenthesizedExpression` and `TSParenthesizedType` nodes that
   * have a single `expression` property containing the expression inside parentheses.
   *
   * @default true
   */
  preserveParens?: boolean
  /**
   * Produce semantic errors with an additional AST pass.
   * Semantic errors depend on symbols and scopes, where the parser does not construct.
   * This adds a small performance overhead.
   *
   * @default false
   */
  showSemanticErrors?: boolean
}
interface PreRenderedChunk {
  name: string
  isEntry: boolean
  isDynamicEntry: boolean
  facadeModuleId?: string
  moduleIds: Array<string>
  exports: Array<string>
}
interface ReactRefreshOptions {
  /**
   * Specify the identifier of the refresh registration variable.
   *
   * @default `$RefreshReg$`.
   */
  refreshReg?: string
  /**
   * Specify the identifier of the refresh signature variable.
   *
   * @default `$RefreshSig$`.
   */
  refreshSig?: string
  emitFullSignatures?: boolean
}
type Severity =  'Error'|
'Warning'|
'Advice';
interface SourceMap {
  file?: string
  mappings: string
  names: Array<string>
  sourceRoot?: string
  sources: Array<string>
  sourcesContent?: Array<string>
  version: number
  x_google_ignoreList?: Array<number>
}
interface Span {
  start: number
  end: number
}
interface StaticExport {
  start: number
  end: number
  entries: Array<StaticExportEntry>
}
interface StaticExportEntry {
  start: number
  end: number
  moduleRequest?: ValueSpan
  /** The name under which the desired binding is exported by the module`. */
  importName: ExportImportName
  /** The name used to export this binding by this module. */
  exportName: ExportExportName
  /** The name that is used to locally access the exported value from within the importing module. */
  localName: ExportLocalName
  /**
   * Whether the export is a TypeScript `export type`.
   *
   * Examples:
   *
   * ```ts
   * export type * from 'mod';
   * export type * as ns from 'mod';
   * export type { foo };
   * export { type foo }:
   * export type { foo } from 'mod';
   * ```
   */
  isType: boolean
}
interface StaticImport {
  /** Start of import statement. */
  start: number
  /** End of import statement. */
  end: number
  /**
   * Import source.
   *
   * ```js
   * import { foo } from "mod";
   * //                   ^^^
   * ```
   */
  moduleRequest: ValueSpan
  /**
   * Import specifiers.
   *
   * Empty for `import "mod"`.
   */
  entries: Array<StaticImportEntry>
}
interface StaticImportEntry {
  /**
   * The name under which the desired binding is exported by the module.
   *
   * ```js
   * import { foo } from "mod";
   * //       ^^^
   * import { foo as bar } from "mod";
   * //       ^^^
   * ```
   */
  importName: ImportName
  /**
   * The name that is used to locally access the imported value from within the importing module.
   * ```js
   * import { foo } from "mod";
   * //       ^^^
   * import { foo as bar } from "mod";
   * //              ^^^
   * ```
   */
  localName: ValueSpan
  /**
   * Whether this binding is for a TypeScript type-only import.
   *
   * `true` for the following imports:
   * ```ts
   * import type { foo } from "mod";
   * import { type foo } from "mod";
   * ```
   */
  isType: boolean
}
/**
* Transpile a JavaScript or TypeScript into a target ECMAScript version.
*
* @param filename The name of the file being transformed. If this is a
* relative path, consider setting the {@link TransformOptions#cwd} option..
* @param sourceText the source code itself
* @param options The options for the transformation. See {@link
* TransformOptions} for more information.
*
* @returns an object containing the transformed code, source maps, and any
* errors that occurred during parsing or transformation.
*/
/**
 * Transpile a JavaScript or TypeScript into a target ECMAScript version.
 *
 * @param filename The name of the file being transformed. If this is a
 * relative path, consider setting the {@link TransformOptions#cwd} option..
 * @param sourceText the source code itself
 * @param options The options for the transformation. See {@link
 * TransformOptions} for more information.
 *
 * @returns an object containing the transformed code, source maps, and any
 * errors that occurred during parsing or transformation.
 */
declare function transform(filename: string, sourceText: string, options?: TransformOptions | undefined | null): TransformResult
/**
* Options for transforming a JavaScript or TypeScript file.
*
* @see {@link transform}
*/
/**
 * Options for transforming a JavaScript or TypeScript file.
 *
 * @see {@link transform}
 */
interface TransformOptions {
  sourceType?: 'script' | 'module' | 'unambiguous' | undefined
  /** Treat the source text as `js`, `jsx`, `ts`, or `tsx`. */
  lang?: 'js' | 'jsx' | 'ts' | 'tsx'
  /**
   * The current working directory. Used to resolve relative paths in other
   * options.
   */
  cwd?: string
  /**
   * Enable source map generation.
   *
   * When `true`, the `sourceMap` field of transform result objects will be populated.
   *
   * @default false
   *
   * @see {@link SourceMap}
   */
  sourcemap?: boolean
  /** Set assumptions in order to produce smaller output. */
  assumptions?: CompilerAssumptions
  /** Configure how TypeScript is transformed. */
  typescript?: TypeScriptOptions
  /** Configure how TSX and JSX are transformed. */
  jsx?: 'preserve' | JsxOptions
  /**
   * Sets the target environment for the generated JavaScript.
   *
   * The lowest target is `es2015`.
   *
   * Example:
   *
   * * 'es2015'
   * * ['es2020', 'chrome58', 'edge16', 'firefox57', 'node12', 'safari11']
   *
   * @default `esnext` (No transformation)
   *
   * @see [esbuild#target](https://esbuild.github.io/api/#target)
   */
  target?: string | Array<string>
  /** Behaviour for runtime helpers. */
  helpers?: Helpers
  /** Define Plugin */
  define?: Record<string, string>
  /** Inject Plugin */
  inject?: Record<string, string | [string, string]>
  /** Decorator plugin */
  decorator?: DecoratorOptions
}
interface TransformResult {
  /**
   * The transformed code.
   *
   * If parsing failed, this will be an empty string.
   */
  code: string
  /**
   * The source map for the transformed code.
   *
   * This will be set if {@link TransformOptions#sourcemap} is `true`.
   */
  map?: SourceMap
  /**
   * The `.d.ts` declaration file for the transformed code. Declarations are
   * only generated if `declaration` is set to `true` and a TypeScript file
   * is provided.
   *
   * If parsing failed and `declaration` is set, this will be an empty string.
   *
   * @see {@link TypeScriptOptions#declaration}
   * @see [declaration tsconfig option](https://www.typescriptlang.org/tsconfig/#declaration)
   */
  declaration?: string
  /**
   * Declaration source map. Only generated if both
   * {@link TypeScriptOptions#declaration declaration} and
   * {@link TransformOptions#sourcemap sourcemap} are set to `true`.
   */
  declarationMap?: SourceMap
  /**
   * Helpers used.
   *
   * @internal
   *
   * Example:
   *
   * ```text
   * { "_objectSpread": "@oxc-project/runtime/helpers/objectSpread2" }
   * ```
   */
  helpersUsed: Record<string, string>
  /**
   * Parse and transformation errors.
   *
   * Oxc's parser recovers from common syntax errors, meaning that
   * transformed code may still be available even if there are errors in this
   * list.
   */
  errors: Array<OxcError>
}
interface TypeScriptOptions {
  jsxPragma?: string
  jsxPragmaFrag?: string
  onlyRemoveTypeImports?: boolean
  allowNamespaces?: boolean
  /**
   * When enabled, type-only class fields are only removed if they are prefixed with the declare modifier:
   *
   * @deprecated
   *
   * Allowing `declare` fields is built-in support in Oxc without any option. If you want to remove class fields
   * without initializer, you can use `remove_class_fields_without_initializer: true` instead.
   */
  allowDeclareFields?: boolean
  /**
   * When enabled, class fields without initializers are removed.
   *
   * For example:
   * ```ts
   * class Foo {
   *    x: number;
   *    y: number = 0;
   * }
   * ```
   * // transform into
   * ```js
   * class Foo {
   *    x: number;
   * }
   * ```
   *
   * The option is used to align with the behavior of TypeScript's `useDefineForClassFields: false` option.
   * When you want to enable this, you also need to set [`crate::CompilerAssumptions::set_public_class_fields`]
   * to `true`. The `set_public_class_fields: true` + `remove_class_fields_without_initializer: true` is
   * equivalent to `useDefineForClassFields: false` in TypeScript.
   *
   * When `set_public_class_fields` is true and class-properties plugin is enabled, the above example transforms into:
   *
   * ```js
   * class Foo {
   *   constructor() {
   *     this.y = 0;
   *   }
   * }
   * ```
   *
   * Defaults to `false`.
   */
  removeClassFieldsWithoutInitializer?: boolean
  /**
   * Also generate a `.d.ts` declaration file for TypeScript files.
   *
   * The source file must be compliant with all
   * [`isolatedDeclarations`](https://www.typescriptlang.org/docs/handbook/release-notes/typescript-5-5.html#isolated-declarations)
   * requirements.
   *
   * @default false
   */
  declaration?: IsolatedDeclarationsOptions
  /**
   * Rewrite or remove TypeScript import/export declaration extensions.
   *
   * - When set to `rewrite`, it will change `.ts`, `.mts`, `.cts` extensions to `.js`, `.mjs`, `.cjs` respectively.
   * - When set to `remove`, it will remove `.ts`/`.mts`/`.cts`/`.tsx` extension entirely.
   * - When set to `true`, it's equivalent to `rewrite`.
   * - When set to `false` or omitted, no changes will be made to the extensions.
   *
   * @default false
   */
  rewriteImportExtensions?: 'rewrite' | 'remove' | boolean
}
interface ValueSpan {
  value: string
  start: number
  end: number
}

//#endregion
export { BindingBuildImportAnalysisPluginConfig, BindingBuiltinPluginName, BindingDynamicImportVarsPluginConfig, BindingGlobImportPluginConfig, BindingHmrOutput, BindingHookResolveIdExtraArgs, BindingIsolatedDeclarationPluginConfig, BindingJsonPluginConfig, BindingManifestPluginConfig, BindingMfManifest, BindingMinifyOptions, BindingModuleFederationPluginOption, BindingModulePreloadPolyfillPluginConfig, BindingRemote, BindingRenderedChunk, BindingReplacePluginConfig, BindingReportPluginConfig, BindingTransformHookExtraArgs, BindingTransformPluginConfig, BindingViteResolvePluginConfig, BindingWatcherEvent, IsolatedDeclarationsOptions, IsolatedDeclarationsResult, ParseResult, ParserOptions, PreRenderedChunk, TransformOptions, TransformResult as TransformResult$1, isolatedDeclaration, moduleRunnerTransform, transform };