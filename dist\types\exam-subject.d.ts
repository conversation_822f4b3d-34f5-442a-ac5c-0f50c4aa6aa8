/**
 * 驾驶证考试科目类型定义
 * 根据实际情况：
 * - 摩托车（D、E、F证）：只有科目二和科目三
 * - 小型汽车（C1、C2、C5证）：有科目一到科目四
 */
export declare enum VehicleType {
    C1 = "C1",// 小型汽车
    C2 = "C2",// 小型自动挡汽车
    C5 = "C5",// 残疾人专用小型自动挡载客汽车
    D = "D",// 普通三轮摩托车
    E = "E",// 普通二轮摩托车
    F = "F"
}
export declare enum ExamSubject {
    SUBJECT_1 = "1",// 科目一：道路交通安全法律、法规和相关知识考试
    SUBJECT_2 = "2",// 科目二：场地驾驶技能考试
    SUBJECT_3 = "3",// 科目三：道路驾驶技能考试
    SUBJECT_4 = "4"
}
export declare enum VehicleCategory {
    CAR = "CAR",// 汽车类
    MOTORCYCLE = "MOTORCYCLE"
}
export declare const VEHICLE_CATEGORY_MAP: Record<VehicleType, VehicleCategory>;
export declare const VEHICLE_EXAM_SUBJECTS: Record<VehicleType, ExamSubject[]>;
export declare const CATEGORY_EXAM_SUBJECTS: Record<VehicleCategory, ExamSubject[]>;
export declare const SUBJECT_NAMES: Record<ExamSubject, string>;
export declare const SUBJECT_SHORT_NAMES: Record<ExamSubject, string>;
export declare const VEHICLE_TYPE_NAMES: Record<VehicleType, string>;
/**
 * 工具函数：获取指定车型的考试科目
 */
export declare function getExamSubjectsForVehicle(vehicleType: VehicleType): ExamSubject[];
/**
 * 工具函数：判断指定车型是否需要考某个科目
 */
export declare function isSubjectRequiredForVehicle(vehicleType: VehicleType, subject: ExamSubject): boolean;
/**
 * 工具函数：获取车型类别
 */
export declare function getVehicleCategory(vehicleType: VehicleType): VehicleCategory;
/**
 * 工具函数：根据车型类别获取所有车型
 */
export declare function getVehicleTypesByCategory(category: VehicleCategory): VehicleType[];
/**
 * 工具函数：验证科目和车型的组合是否有效
 */
export declare function isValidSubjectVehicleCombination(subject: ExamSubject, vehicleType: VehicleType): boolean;
/**
 * 扩展的考生数据类型，基于原有结构
 */
export interface ExamineeRecord {
    id?: number;
    name: string;
    id_number: string;
    allowed_car_type: VehicleType;
    appointment_result: string;
    sort_time: string;
    exam_date: string;
    exam_desc: string;
    exam_car_type: VehicleType;
    exam_venue: string;
    detailed_address: string;
    exam_subject: ExamSubject;
    created_at: string;
}
/**
 * 考试配置类型
 */
export interface ExamConfig {
    supportedVehicleTypes: VehicleType[];
    enabledSubjects: ExamSubject[];
    enableMotorcycleExams: boolean;
    enableCarExams: boolean;
}
/**
 * 统计数据的车型映射
 */
export interface SubjectStatsByVehicle {
    [VehicleType.C1]: number;
    [VehicleType.C2]: number;
    [VehicleType.C5]: number;
    [VehicleType.D]?: number;
    [VehicleType.E]?: number;
    [VehicleType.F]?: number;
}
