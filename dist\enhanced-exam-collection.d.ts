/**
 * 使用示例：集成新的驾驶证考试科目分类系统
 *
 * 这个文件展示了如何在现有代码中使用新的科目分类逻辑
 */
interface ExamineeData {
    xm?: string;
    sfzmhm?: string;
    zt?: string;
    pxsjStr?: string;
    name?: string;
    id_number?: string;
    allowed_car_type?: string;
    appointment_result?: string;
    sort_time?: string;
    exam_date?: string;
    exam_desc?: string;
    exam_car_type?: string;
    exam_venue?: string;
    detailed_address?: string;
    exam_subject?: string;
    created_at?: string;
}
/**
 * 处理考生数据的增强版本
 * 集成了新的科目分类验证
 */
declare function enhancedSaveExamineeToDatabase(examinee: ExamineeData, examInfo: {
    examDate: string;
    examDesc: string;
    examCarType: string;
    examVenue: string;
    detailedAddress: string;
    examSubject: string;
    examinationName: string;
}, db: any): {
    success: boolean;
    error?: string;
};
/**
 * 增强的科目数据采集函数
 * 支持扩展科目的处理
 */
declare function enhancedCollectExamData(cookie: string, provinceCode: string, fzjg: string, db: any, config: any): Promise<void>;
/**
 * 生成增强的统计报告
 */
declare function generateEnhancedReport(db: any): Promise<void>;
/**
 * 配置验证示例
 */
declare function validateConfiguration(config: any): boolean;
export { enhancedSaveExamineeToDatabase, enhancedCollectExamData, generateEnhancedReport, validateConfiguration };
