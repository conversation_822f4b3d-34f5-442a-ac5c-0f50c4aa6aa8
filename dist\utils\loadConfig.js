import path from "path";
import fs from "fs";
import { VehicleType, ExamSubject } from '../types/exam-subject.js';
import ExamSubjectManager from './exam-subject-manager.js';
// 默认配置
const DEFAULT_CONFIG = {
    province: "广东",
    fzjg: "粤K", // 城市车牌号
    cronSchedule: "0 8 * * *", // 每天早上 8 点执行
    databasePath: path.join(process.cwd(), "data", "examinees.db"),
    daysAhead: 15, // 默认向前查询15天的考试
    statisticsDays: 7, // 默认只统计最近7天的数据
    pushKey: [],
    dateRange: {
        startDate: new Date().toISOString().split("T")[0], // 默认开始日期为今天
        endDate: "", // 将在程序中动态设置
    },
    statisticsExaminationName: "",
    disableUpload: false,
    disablePush: false,
    subjects: ["1", "2", "3", "4"], // 默认采集科目1-4
    enabledVehicleTypes: [VehicleType.C1, VehicleType.C2, VehicleType.C5], // 默认只启用小型汽车
    enableMotorcycleExams: false, // 默认不启用摩托车考试
    enableCarExams: true, // 默认启用汽车考试
};
function parseBoolEnv(v) {
    if (v == null)
        return undefined;
    const s = String(v).trim().toLowerCase();
    if (s === "1" || s === "true")
        return true;
    if (s === "0" || s === "false")
        return false;
    return undefined;
}
/**
 * 处理科目互斥逻辑，避免重复采集
 * @param subjects 原始科目列表
 * @param enabledVehicleTypes 启用的车型列表
 * @returns 处理后的科目列表和相关配置
 */
function resolveSubjects(subjects, enabledVehicleTypes) {
    const subjectSet = new Set(subjects);
    // 如果包含"8"（全科目），忽略"1".."4"，避免重复
    if (subjectSet.has("8")) {
        const finalSubjects = ["8"];
        return {
            subjects: finalSubjects,
            collectionConfig: ExamSubjectManager.getSubjectCollectionConfig(finalSubjects)
        };
    }
    // 如果包含"9"（科目二三），忽略"2"、"3"，避免重复
    if (subjectSet.has("9")) {
        const result = Array.from(subjectSet);
        const finalSubjects = result.filter(s => s !== "2" && s !== "3");
        return {
            subjects: finalSubjects,
            collectionConfig: ExamSubjectManager.getSubjectCollectionConfig(finalSubjects)
        };
    }
    const finalSubjects = Array.from(subjectSet);
    // 根据启用的车型过滤科目
    const enabledSubjects = ExamSubjectManager.getEnabledSubjectsForConfig(finalSubjects, enabledVehicleTypes);
    return {
        subjects: finalSubjects,
        collectionConfig: ExamSubjectManager.getSubjectCollectionConfig(finalSubjects)
    };
}
// 从配置文件加载配置
function loadConfig() {
    const config = { ...DEFAULT_CONFIG };
    try {
        const configPath = path.join(process.cwd(), "app-config.json");
        if (fs.existsSync(configPath)) {
            const userConfig = JSON.parse(fs.readFileSync(configPath, "utf-8"));
            // 加载用户配置，覆盖默认值
            if (userConfig.province)
                config.province = userConfig.province;
            if (userConfig.fzjg)
                config.fzjg = userConfig.fzjg;
            if (userConfig.cron_schedule)
                config.cronSchedule = userConfig.cron_schedule;
            if (userConfig.days_ahead)
                config.daysAhead = userConfig.days_ahead;
            if (userConfig.statistics_days)
                config.statisticsDays = userConfig.statistics_days;
            if (userConfig.push_key)
                config.pushKey = userConfig.push_key;
            if (userConfig.database_path)
                config.databasePath = userConfig.database_path;
            if (userConfig.statistics_examination_name)
                config.statisticsExaminationName =
                    userConfig.statistics_examination_name;
            // 新增：读取禁用开关（配置文件字段名使用下划线）
            if (typeof userConfig.disable_upload === "boolean")
                config.disableUpload = userConfig.disable_upload;
            if (typeof userConfig.disable_push === "boolean")
                config.disablePush = userConfig.disable_push;
            // 新增：读取采集科目配置
            if (Array.isArray(userConfig.subjects)) {
                config.subjects = userConfig.subjects.map(String);
            }
            // 新增：读取车型配置
            if (Array.isArray(userConfig.enabled_vehicle_types)) {
                config.enabledVehicleTypes = userConfig.enabled_vehicle_types
                    .filter(type => Object.values(VehicleType).includes(type));
            }
            // 新增：读取摩托车和汽车考试开关
            if (typeof userConfig.enable_motorcycle_exams === "boolean") {
                config.enableMotorcycleExams = userConfig.enable_motorcycle_exams;
            }
            if (typeof userConfig.enable_car_exams === "boolean") {
                config.enableCarExams = userConfig.enable_car_exams;
            }
            // 日期范围会在程序中动态设置，这里只设置初始值
            config.dateRange = {
                startDate: config.dateRange.startDate,
                endDate: "",
            };
            console.log("成功从配置文件加载配置");
        }
        else {
            console.log("配置文件不存在，使用默认配置");
        }
    }
    catch (error) {
        console.error("加载配置文件失败:", error);
        console.log("使用默认配置");
    }
    // 环境变量覆盖（优先级高于文件配置）
    const envDisableUpload = parseBoolEnv(process.env.DISABLE_UPLOAD);
    const envDisablePush = parseBoolEnv(process.env.DISABLE_PUSH);
    if (typeof envDisableUpload === "boolean")
        config.disableUpload = envDisableUpload;
    if (typeof envDisablePush === "boolean")
        config.disablePush = envDisablePush;
    // 环境变量 SUBJECTS 支持逗号分隔的科目列表
    if (process.env.SUBJECTS) {
        config.subjects = process.env.SUBJECTS.split(",").map(s => s.trim());
    }
    // 根据车型类别开关自动配置启用的车型
    if (config.enableCarExams && config.enableMotorcycleExams) {
        // 两种都启用，保持配置的车型
    }
    else if (config.enableCarExams) {
        // 只启用汽车，过滤掉摩托车车型
        config.enabledVehicleTypes = config.enabledVehicleTypes.filter(type => [VehicleType.C1, VehicleType.C2, VehicleType.C5].includes(type));
    }
    else if (config.enableMotorcycleExams) {
        // 只启用摩托车，过滤掉汽车车型
        config.enabledVehicleTypes = config.enabledVehicleTypes.filter(type => [VehicleType.D, VehicleType.E, VehicleType.F].includes(type));
    }
    else {
        // 都不启用，使用默认（汽车）
        config.enabledVehicleTypes = [VehicleType.C1, VehicleType.C2, VehicleType.C5];
        config.enableCarExams = true;
    }
    // 应用科目互斥逻辑
    const subjectResult = resolveSubjects(config.subjects, config.enabledVehicleTypes);
    config.subjects = subjectResult.subjects;
    console.log(`启用车型: ${config.enabledVehicleTypes.join(", ")}`);
    console.log(`最终采集科目: ${config.subjects.join(", ")}`);
    console.log(`标准科目: ${subjectResult.collectionConfig.standardSubjects.join(", ")}`);
    if (subjectResult.collectionConfig.hasExtendedSubjects) {
        console.log(`扩展科目: ${subjectResult.collectionConfig.extendedSubjects.join(", ")}`);
    }
    return config;
}
export default loadConfig;
//# sourceMappingURL=loadConfig.js.map