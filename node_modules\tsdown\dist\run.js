#!/usr/bin/env node
import { resolve<PERSON>omma, toArray } from "./general-CPYs4M61.js";
import { logger } from "./logger-DYeY_1KP.js";
import { version } from "./package-CtZQ6FbS.js";
import process from "node:process";
import { dim } from "ansis";
import Debug from "debug";
import { VERSION } from "rolldown";
import module from "node:module";
import { cac } from "cac";

//#region src/cli.ts
const cli = cac("tsdown");
cli.help().version(version);
cli.command("[...files]", "Bundle files", { ignoreOptionDefaultValue: true }).option("-c, --config <filename>", "Use a custom config file").option("--no-config", "Disable config file").option("--format <format>", "Bundle format: esm, cjs, iife", { default: "esm" }).option("--clean", "Clean output directory").option("--external <module>", "Mark dependencies as external").option("--minify", "Minify output").option("--debug [scope]", "Show debug logs").option("--target <target>", "Bundle target, e.g \"es2015\", \"esnext\"").option("--silent", "Suppress non-error logs").option("-d, --out-dir <dir>", "Output directory", { default: "dist" }).option("--treeshake", "Tree-shake bundle", { default: true }).option("--sourcemap", "Generate source map", { default: false }).option("--shims", "Enable cjs and esm shims ", { default: false }).option("--platform <platform>", "Target platform", { default: "node" }).option("--dts", "Generate dts files").option("--publint", "Enable publint", { default: false }).option("--unused", "Enable unused dependencies check", { default: false }).option("-w, --watch [path]", "Watch mode").option("--from-vite [vitest]", "Reuse config from Vite or Vitest").option("--report", "Size report", { default: true }).option("--env.* <value>", "Define compile-time env variables").option("--on-success <command>", "Command to run on success").option("--copy <dir>", "Copy files to output dir").option("--public-dir <dir>", "Alias for --copy, deprecated").option("--tsconfig <tsconfig>", "Set tsconfig path").action(async (input, flags) => {
	logger.setSilent(!!flags.silent);
	logger.info(`tsdown ${dim`v${version}`} powered by rolldown ${dim`v${VERSION}`}`);
	const { build: build$1 } = await import("./index.js");
	if (input.length > 0) flags.entry = input;
	await build$1(flags);
});
cli.command("migrate", "Migrate from tsup to tsdown").option("-c, --cwd <dir>", "Working directory").option("-d, --dry-run", "Dry run").action(async (args) => {
	const { migrate } = await import("./migrate-CB-PFTao.js");
	await migrate(args);
});
async function runCLI() {
	cli.parse(process.argv, { run: false });
	if (cli.options.debug) {
		let namespace;
		if (cli.options.debug === true) namespace = "tsdown:*";
		else namespace = resolveComma(toArray(cli.options.debug)).map((v) => `tsdown:${v}`).join(",");
		const enabled = Debug.disable();
		if (enabled) namespace += `,${enabled}`;
		Debug.enable(namespace);
		Debug("tsdown:debug")("Debugging enabled", namespace);
	}
	try {
		await cli.runMatchedCommand();
	} catch (error) {
		logger.error(error);
		process.exit(1);
	}
}

//#endregion
//#region src/run.ts
try {
	module.enableCompileCache?.();
} catch {}
runCLI();

//#endregion