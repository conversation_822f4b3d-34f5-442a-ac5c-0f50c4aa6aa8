import { VehicleType } from '../types/exam-subject.js';
interface ConfigType {
    province: string;
    fzjg: string;
    cronSchedule: string;
    databasePath: string;
    daysAhead: number;
    statisticsDays: number;
    pushKey: string[];
    dateRange: {
        startDate: string;
        endDate: string;
    };
    statisticsExaminationName: string;
    disableUpload: boolean;
    disablePush: boolean;
    subjects: string[];
    enabledVehicleTypes: VehicleType[];
    enableMotorcycleExams: boolean;
    enableCarExams: boolean;
}
declare function loadConfig(): ConfigType;
export default loadConfig;
