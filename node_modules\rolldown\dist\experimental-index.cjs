"use strict";
const require_chunk = require('./shared/chunk-qZFfknuJ.cjs');
const require_src = require('./shared/src-DU3H36fs.cjs');
const require_parse_ast_index = require('./shared/parse-ast-index-CfcTAL_S.cjs');
require('./shared/filter-index-DblXSw9s.cjs');
const node_url = require_chunk.__toESM(require("node:url"));

//#region src/api/experimental.ts
/**
* This is an experimental API. It's behavior may change in the future.
*
* Calling this API will only execute the scan stage of rolldown.
*/
const experimental_scan = async (input) => {
	const { bundler, stopWorkers } = await require_src.createBundler(input, {});
	const output = await bundler.scan();
	require_src.handleOutputErrors(output);
	await stopWorkers?.();
};

//#endregion
//#region src/plugin/parallel-plugin.ts
function defineParallelPlugin(pluginPath) {
	return (options) => {
		return { _parallel: {
			fileUrl: (0, node_url.pathToFileURL)(pluginPath).href,
			options
		} };
	};
}

//#endregion
//#region src/builtin-plugin/alias-plugin.ts
function aliasPlugin(config) {
	return new require_src.BuiltinPlugin("builtin:alias", config);
}

//#endregion
//#region src/builtin-plugin/replace-plugin.ts
/**
* Replaces targeted strings in files while bundling.
*
* @example
* // Basic usage
* ```js
* replacePlugin({
*   'process.env.NODE_ENV': JSON.stringify('production'),
*    __buildDate__: () => JSON.stringify(new Date()),
*    __buildVersion: 15
* })
* ```
* @example
* // With options
* ```js
* replacePlugin({
*   'process.env.NODE_ENV': JSON.stringify('production'),
*   __buildDate__: () => JSON.stringify(new Date()),
*   __buildVersion: 15
* }, {
*   preventAssignment: false,
* })
* ```
*/
function replacePlugin(values = {}, options = {}) {
	return new require_src.BuiltinPlugin("builtin:replace", {
		...options,
		values
	});
}

//#endregion
//#region src/builtin-plugin/transform-plugin.ts
function transformPlugin(config) {
	if (config) config = {
		...config,
		include: require_src.normalizedStringOrRegex(config.include),
		exclude: require_src.normalizedStringOrRegex(config.exclude),
		jsxRefreshInclude: require_src.normalizedStringOrRegex(config.jsxRefreshInclude),
		jsxRefreshExclude: require_src.normalizedStringOrRegex(config.jsxRefreshExclude)
	};
	return new require_src.BuiltinPlugin("builtin:transform", config);
}

//#endregion
exports.aliasPlugin = aliasPlugin
exports.buildImportAnalysisPlugin = require_src.buildImportAnalysisPlugin
exports.composePlugins = require_src.composeJsPlugins
exports.defineParallelPlugin = defineParallelPlugin
exports.dynamicImportVarsPlugin = require_src.dynamicImportVarsPlugin
exports.importGlobPlugin = require_src.importGlobPlugin
Object.defineProperty(exports, 'isolatedDeclaration', {
  enumerable: true,
  get: function () {
    return import_binding.isolatedDeclaration;
  }
});
exports.isolatedDeclarationPlugin = require_src.isolatedDeclarationPlugin
exports.jsonPlugin = require_src.jsonPlugin
exports.loadFallbackPlugin = require_src.loadFallbackPlugin
exports.manifestPlugin = require_src.manifestPlugin
exports.moduleFederationPlugin = require_src.moduleFederationPlugin
exports.modulePreloadPolyfillPlugin = require_src.modulePreloadPolyfillPlugin
Object.defineProperty(exports, 'moduleRunnerTransform', {
  enumerable: true,
  get: function () {
    return import_binding.moduleRunnerTransform;
  }
});
exports.replacePlugin = replacePlugin
exports.reportPlugin = require_src.reportPlugin
exports.scan = experimental_scan
Object.defineProperty(exports, 'transform', {
  enumerable: true,
  get: function () {
    return import_binding.transform;
  }
});
exports.transformPlugin = transformPlugin
exports.viteResolvePlugin = require_src.viteResolvePlugin
exports.wasmFallbackPlugin = require_src.wasmFallbackPlugin
exports.wasmHelperPlugin = require_src.wasmHelperPlugin