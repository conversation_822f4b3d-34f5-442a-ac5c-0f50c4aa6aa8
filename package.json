{"name": "examinees-collection", "version": "1.0.0", "description": "检测缺口验证码", "main": "index.js", "type": "module", "scripts": {"build": "tsc --outDir dist --module esnext --target es2020 --moduleResolution node --esModuleInterop --allowSyntheticDefaultImports --skipLib<PERSON>heck", "build:debug": "tsdown --sourcemap --format=esm", "build:windows": "powershell -ExecutionPolicy Bypass -File build-en.ps1", "build:exe": "powershell -ExecutionPolicy Bypass -File build-en.ps1", "build:portable": "npm run build && node -e \"console.log('构建完成，使用 npm run start 启动应用')\"", "build:mac": "./build.sh --mac", "start": "npm run build && node dist/index.js", "start:debug": "tsdown --sourcemap --format=esm && node dist/index.js", "detect": "node detect-gaps.js"}, "dependencies": {"jimp": "^0.22.10", "node-cron": "^3.0.3", "sql.js": "^1.13.0", "undici": "^7.8.0"}, "devDependencies": {"@types/node-cron": "^3.0.11", "@types/sql.js": "^1.4.9", "tsdown": "^0.11.1", "tsx": "^4.20.4", "typescript": "^5.8.3"}}