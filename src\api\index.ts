import type {
  AppointmentResultsRes,
  ExamCenterListRes,
  ExamCenterOptionsListRes,
  ExamSessionListRes,
  UploadFileRes,
} from "./type";

// 对Node.js 18+添加内置fetch类型支持
import { fetch, Headers, FormData } from "undici";
import type { RequestRedirect } from "undici";
import { Blob } from "node:buffer";
import loadConfig from "../utils/loadConfig.js";

const RUNTIME_CONFIG = loadConfig();

type BaseResponse = { data: string; code: string };

function getBaseHeaders({ province }: { province: string }) {
  return {
    accept: "application/json, text/javascript, */*; q=0.01",
    "accept-language": "en-US,en;q=0.9",
    "cache-control": "no-cache",
    "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
    pragma: "no-cache",
    "prefer-key": "*************",
    "sec-ch-ua":
      '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"macOS"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "x-request-from": "website",
    "x-requested-with": "XMLHttpRequest",
    Referer: `https://${province}.122.gov.cn/web/html/?type=3&ticket=NIL&code=E_A_1023`,
    "Referrer-Policy": "strict-origin-when-cross-origin",
  };
}

async function getCookie({ province }: { province: string }): Promise<string> {
  const res = await fetch(`https://${province}.122.gov.cn/favicon.ico`, {
    headers: {
      ...getBaseHeaders({ province }),
      accept:
        "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
    },
    body: null,
    method: "GET",
  });
  const cookie = res.headers.get("Set-Cookie");
  return cookie ?? "";
}

function getCaptchaPre({
  cookie,
  province,
}: {
  cookie: string;
  province: string;
}) {
  return fetch("https://gd.122.gov.cn/web/api", {
    headers: {
      ...getBaseHeaders({ province }),
      cookie,
    },
    body: "method=%2Fnetdrv%2Ftmri%2Fcaptcha%2FcheckType&params=%7B%22checktype%22%3A%22yfxSBDlQbgZSYnBo%22%7D",
    method: "POST",
  }).then((res) => res.json());
}

async function getCaptcha({
  seed,
  cookie,
  province,
}: {
  seed: string;
  cookie: string;
  province: string;
}): Promise<BaseResponse> {
  const res = await fetch("https://gd.122.gov.cn/web/api", {
    headers: {
      ...getBaseHeaders({ province }),
      cookie,
    },
    body: `method=%2Fnetdrv%2Ftmri%2Fcaptcha%2FsliderImg&params=%7B%22seed%22%3A${seed}%7D`,
    method: "POST",
  });
  const data = await res.json();
  return data as BaseResponse;
}

function verifyCaptcha({
  x,
  y,
  cookie,
  province,
}: {
  x: string;
  y: string;
  cookie: string;
  province: string;
}): Promise<BaseResponse> {
  return fetch("https://gd.122.gov.cn/web/api", {
    headers: {
      ...getBaseHeaders({ province }),
      cookie,
    },
    body: `method=%2Fnetdrv%2Ftmri%2Fcaptcha%2FcheckchCoordinate&params=%7B%22x%22%3A%22${x}%22%2C%22y%22%3A%22${y}%22%7D`,
    method: "POST",
  }).then((res) => res.json() as Promise<BaseResponse>);
}

// 获取考场列表
function getExamCenterList({
  token,
  cookie,
  startDate,
  endDate,
  province,
}: {
  token: string;
  cookie: string;
  startDate: string;
  endDate: string;
  province: string;
}): Promise<ExamCenterListRes> {
  const body = `method=/netdrv/pub/getLimitPub&params={"page":1,"type":"3","fzjg":"粤A","kskm":"1","ksdd":"","kscx":null,"jhlx":"","ywlx":"","startDate":"${startDate}","endDate":"${endDate}","csessionid":"","sig":"","token":"${token}","scene":""}`;
  return fetch("https://gd.122.gov.cn/web/api", {
    headers: {
      ...getBaseHeaders({ province }),
      cookie,
    },
    body: encodeURIComponent(body),
    method: "POST",
  }).then((res) =>
    res.json().then((response: any) => {
      const { data } = response;
      return JSON.parse(data);
    })
  );
}

function getExamCenterOptionsList({
  cookie,
  fzjg,
  kskm,
  province,
}: {
  cookie: string;
  fzjg: string;
  kskm: string;
  province: string;
}): Promise<ExamCenterOptionsListRes> {
  const data = `{"fzjg":"${fzjg}","kskm":"${kskm}"}`;

  return fetch(`https://${province}.122.gov.cn/web/api`, {
    headers: {
      ...getBaseHeaders({ province }),
      Referer: `https://${province}.122.gov.cn/web/html/?type=3&ticket=NIL&code=E_A_1023`,
      cookie,
    },
    body:
      "method=%2Fnetdrv%2Fpub%2FgetExamSite&params=" + encodeURIComponent(data),
    method: "POST",
  }).then(async (res) => {
    const dataJson = await res.json();
    const { data } = dataJson as BaseResponse;
    return JSON.parse(data);
  });
}

// 获取考试场次列表
function getExamSessionList({
  cookie,
  ksdd,
  kskm,
  fzjg,
  startDate,
  endDate,
  province,
}: {
  cookie: string;
  ksdd: string;
  kskm: string;
  fzjg: string;
  startDate: string;
  endDate: string;
  province: string;
}): Promise<ExamSessionListRes> {
  const bodyObj: any = {
    type: "3",
    fzjg: fzjg,
    kskm: kskm,
    ksdd: ksdd,
    startDate: startDate,
    endDate: endDate,
  };

  if (kskm === "1") {
    bodyObj.jhlx = "";
    bodyObj.ywlx = "";
  }

  const body = JSON.stringify(bodyObj);

  return fetch(`https://${province}.122.gov.cn/web/api`, {
    headers: {
      ...getBaseHeaders({ province }),
      cookie,
    },
    body:
      "method=%2Fnetdrv%2Fpub%2FgetLimitDetailPub&params=" +
      encodeURIComponent(body),
    method: "POST",
  }).then((res) =>
    res.json().then((response: any) => {
      const { data } = response;
      if (kskm === "4" || kskm === "1") {
        console.log("[考试场次列表]", kskm, data);
      }

      return JSON.parse(data);
    })
  );
}

// 获取预约结果
async function getAppointmentResults({
  cookie,
  xh,
  province,
}: {
  cookie: string;
  xh: string;
  province: string;
}): Promise<AppointmentResultsRes> {
  const body = `{"type":"3","xh":${xh}}`;
  const res = await fetch(`https://${province}.122.gov.cn/web/api`, {
    headers: {
      ...getBaseHeaders({ province }),
      cookie,
    },
    body:
      "method=%2Fnetdrv%2Fpub%2FgetLimitPubPerson&params=" + encodeURIComponent(body),
    method: "POST",
  });
  const { data } = (await res.json()) as BaseResponse;

  await fetch(`https://${province}.122.gov.cn/web/api`, {
    headers: {
      ...getBaseHeaders({ province }),
      cookie,
    },
    body:
      "method=/netdrv/pub/getLimitPubPerson&params=" +
      encodeURIComponent(`{"type":"3","xh":${xh},"lstd":"1"}`),
    method: "POST",
  });
  return JSON.parse(data);
}

function uploadFile({
  file,
  filename,
  permission = "0",
  strategy_id = "1",
}: {
  file: Buffer;
  filename: string;
  permission?: string;
  strategy_id?: string;
}): Promise<UploadFileRes> {
  if (RUNTIME_CONFIG.disableUpload) {
    console.log("[upload] 已禁用，根据配置跳过上传: ", filename);
    // 构造与 UploadFileRes 兼容的占位返回
    const placeholder: UploadFileRes = {
      status: true,
      message: "skipped",
      data: {
        key: "",
        name: filename,
        pathname: "",
        origin_name: filename,
        size: file.byteLength,
        mimetype: "image/png",
        extension: "png",
        md5: "",
        sha1: "",
        links: {
          url: "",
          html: "",
          bbcode: "",
          markdown: "",
          markdown_with_link: "",
          thumbnail_url: "",
        },
      },
    };
    return Promise.resolve(placeholder);
  }

  const myHeaders = new Headers();
  myHeaders.append("Accept", "application/json");

  const formdata = new FormData();
  // 将Buffer转换为Blob对象
  const blob = new Blob([
    new Uint8Array(file.buffer, file.byteOffset, file.byteLength),
  ]);
  formdata.append("file", blob, filename);
  formdata.append("permission", permission);
  formdata.append("strategy_id", strategy_id);

  const requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: formdata,
    redirect: "follow" as RequestRedirect,
  };

  return fetch("https://pic.wxloc.com/api/v1/upload", requestOptions).then(
    (response) => {
      return response.json() as Promise<UploadFileRes>;
    }
  );
}

// 消息推送
function sendMessage({
  pushKey,
  imageUrl,
}: {
  pushKey: string;
  imageUrl: string;
}) {
  if (RUNTIME_CONFIG.disablePush) {
    console.log("[push] 已禁用，根据配置跳过推送: ", pushKey);
    return;
  }
  if (!imageUrl) {
    console.log("[push] imageUrl 为空，跳过推送");
    return;
  }
  const url = `https://api2.pushdeer.com/message/push?pushkey=${pushKey}&text=${imageUrl}&type=image`;

  fetch(url, {
    method: "GET",
  });
}

export {
  getCookie,
  getCaptchaPre,
  getCaptcha,
  verifyCaptcha,
  getExamCenterList,
  getExamCenterOptionsList,
  getExamSessionList,
  getAppointmentResults,
  uploadFile,
  sendMessage,
};
