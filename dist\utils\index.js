import province from "../constans/province.json" with { type: "json" };
import license from "../constans/licenseName.json" with { type: "json" };
export const getProvinceCode = (provinceName) => {
    return province[provinceName];
};
export const getLicenseName = (licenseCode) => {
    return license[licenseCode];
};
/**
 * 获取本地日期的格式字符串（YYYY-MM-DD）
 * @param date 日期对象，默认为当前日期
 * @returns 本地日期字符串，格式为YYYY-MM-DD
 */
export function getLocalDateString(date = new Date()) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
}
//# sourceMappingURL=index.js.map