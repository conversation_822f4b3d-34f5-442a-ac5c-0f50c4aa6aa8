﻿import province from "../constans/province.json" with { type: "json" };
import license from "../constans/licenseName.json" with { type: "json" };
export const getProvinceCode = (provinceName) => {
    return province[provinceName];
};
export const getLicenseName = (licenseCode) => {
    return license[licenseCode];
};
/**
 * 鑾峰彇鏈湴鏃ユ湡鐨勬牸寮忓瓧绗︿覆锛圷YYY-MM-DD锛? * @param date 鏃ユ湡瀵硅薄锛岄粯璁や负褰撳墠鏃ユ湡
 * @returns 鏈湴鏃ユ湡瀛楃涓诧紝鏍煎紡涓篩YYY-MM-DD
 */
export function getLocalDateString(date = new Date()) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
}
//# sourceMappingURL=index.js.map
