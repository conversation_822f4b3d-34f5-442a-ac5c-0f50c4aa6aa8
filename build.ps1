# Windows PowerShell构建脚本
# 用于构建驾考预约数据采集系统

param(
    [string]$Target = "win"
)

Write-Host "=== 驾考预约数据采集系统 Windows 构建脚本 ===" -ForegroundColor Green
Write-Host ""

# 检查Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未找到Node.js，请先安装Node.js" -ForegroundColor Red
    exit 1
}

# 检查npm
try {
    $npmVersion = npm --version
    Write-Host "✅ npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未找到npm" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 安装依赖
Write-Host "📦 安装依赖..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 依赖安装失败" -ForegroundColor Red
    exit 1
}
Write-Host "✅ 依赖安装完成" -ForegroundColor Green
Write-Host ""

# 构建TypeScript
Write-Host "🔨 构建TypeScript项目..." -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ TypeScript构建失败" -ForegroundColor Red
    exit 1
}
Write-Host "✅ TypeScript构建完成" -ForegroundColor Green
Write-Host ""

# 修复JSON导入属性
Write-Host "🔧 修复ES模块JSON导入..." -ForegroundColor Yellow
$indexPath = "dist\utils\index.js"
if (Test-Path $indexPath) {
    $content = Get-Content $indexPath -Raw
    $content = $content -replace 'import province from "\.\.\/constans\/province\.json";', 'import province from "../constans/province.json" with { type: "json" };'
    $content = $content -replace 'import license from "\.\.\/constans\/licenseName\.json";', 'import license from "../constans/licenseName.json" with { type: "json" };'
    Set-Content $indexPath $content -Encoding UTF8
    Write-Host "✅ JSON导入修复完成" -ForegroundColor Green
} else {
    Write-Host "⚠️ 未找到 $indexPath 文件" -ForegroundColor Yellow
}
Write-Host ""

# 检查SEA支持
Write-Host "🔍 检查Single Executable Application支持..." -ForegroundColor Yellow
try {
    $null = node --experimental-sea-config --help 2>$null
    $seaSupported = $true
} catch {
    $seaSupported = $false
}

if (-not $seaSupported) {
    Write-Host "⚠️ 警告: 当前Node.js版本可能不支持SEA，将跳过可执行文件构建" -ForegroundColor Yellow
    Write-Host "✅ 项目构建完成，可以使用 npm run start 启动" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 使用方法:" -ForegroundColor Cyan
    Write-Host "  npm run start" -ForegroundColor White
    Write-Host "  或者直接运行: node dist/index.js" -ForegroundColor White
    exit 0
}

# 创建SEA配置
Write-Host "📝 创建SEA配置文件..." -ForegroundColor Yellow
$seaConfig = @{
    main = "dist/index.js"
    output = "sea-prep.blob"
    disableExperimentalSEAWarning = $true
    useSnapshot = $false
    useCodeCache = $true
} | ConvertTo-Json

Set-Content "sea-config.json" $seaConfig -Encoding UTF8

# 生成SEA blob
Write-Host "🔄 生成SEA blob文件..." -ForegroundColor Yellow
node --experimental-sea-config sea-config.json
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ SEA blob生成失败" -ForegroundColor Red
    exit 1
}

# 复制Node.js可执行文件
Write-Host "📋 复制Node.js可执行文件..." -ForegroundColor Yellow
$nodePath = (Get-Command node).Source
Copy-Item $nodePath "examinees-collection.exe"

# 检查postject
Write-Host "🔧 检查postject工具..." -ForegroundColor Yellow
try {
    $null = npx postject --help 2>$null
} catch {
    Write-Host "📦 安装postject..." -ForegroundColor Yellow
    npm install -g postject
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ postject安装失败" -ForegroundColor Red
        exit 1
    }
}

# 注入SEA blob
Write-Host "💉 注入SEA blob到可执行文件..." -ForegroundColor Yellow
npx postject examinees-collection.exe NODE_SEA_BLOB sea-prep.blob --sentinel-fuse NODE_SEA_FUSE_fce680ab2cc467b6e072b8b5df1996b2
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ SEA blob注入失败" -ForegroundColor Red
    exit 1
}

# 清理临时文件
Write-Host "🧹 清理临时文件..." -ForegroundColor Yellow
Remove-Item "sea-config.json" -ErrorAction SilentlyContinue
Remove-Item "sea-prep.blob" -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "🎉 构建完成！" -ForegroundColor Green
Write-Host ""

# 显示文件信息
if (Test-Path "examinees-collection.exe") {
    $fileInfo = Get-Item "examinees-collection.exe"
    Write-Host "📁 输出文件: examinees-collection.exe" -ForegroundColor Cyan
    Write-Host "📊 文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "🚀 使用方法:" -ForegroundColor Cyan
Write-Host "  1. 确保配置文件 app-config.json 存在" -ForegroundColor White
Write-Host "  2. 运行: .\examinees-collection.exe" -ForegroundColor White
Write-Host ""
Write-Host "📋 注意事项:" -ForegroundColor Cyan
Write-Host "  - 首次运行可能需要较长时间" -ForegroundColor White
Write-Host "  - 确保MariaDB服务正在运行（如果使用MariaDB模式）" -ForegroundColor White
Write-Host "  - 可执行文件包含了所有依赖，可以独立运行" -ForegroundColor White

Write-Host ""
Write-Host "✅ 构建脚本执行完成" -ForegroundColor Green
