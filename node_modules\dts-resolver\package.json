{"name": "dts-resolver", "version": "1.1.1", "description": "Resolves TypeScript declaration files for dependencies.", "type": "module", "license": "MIT", "homepage": "https://github.com/sxzz/dts-resolver#readme", "bugs": {"url": "https://github.com/sxzz/dts-resolver/issues"}, "repository": {"type": "git", "url": "git+https://github.com/sxzz/dts-resolver.git"}, "author": "三咲智子 <PERSON> <<EMAIL>>", "funding": "https://github.com/sponsors/sxzz", "files": ["dist"], "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "dependencies": {"oxc-resolver": "^7.0.1", "pathe": "^2.0.3"}, "devDependencies": {"@babel/parser": "^7.27.1", "@babel/types": "^7.27.1", "@sxzz/eslint-config": "^6.2.0", "@sxzz/prettier-config": "^2.2.1", "@types/debug": "^4.1.12", "@types/node": "^22.15.3", "@types/yargs": "^17.0.33", "@vue/reactivity": "^3.5.13", "bumpp": "^10.1.0", "eslint": "^9.26.0", "fast-glob": "^3.3.3", "magic-string": "^0.30.17", "magic-string-ast": "^0.9.1", "prettier": "^3.5.3", "tsdown": "^0.11.0-beta.3", "typescript": "^5.8.3", "vitest": "^3.1.2", "vue": "^3.5.13"}, "engines": {"node": ">=20.18.0"}, "prettier": "@sxzz/prettier-config", "scripts": {"lint": "eslint --cache .", "lint:fix": "pnpm run lint --fix", "build": "tsdown", "dev": "tsdown --watch", "test": "vitest", "typecheck": "tsc --noEmit", "format": "prettier --cache --write .", "release": "bumpp && pnpm publish"}}