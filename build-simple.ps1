# 简化的Windows构建脚本

Write-Host "=== 驾考预约数据采集系统构建脚本 ===" -ForegroundColor Green
Write-Host ""

# 构建TypeScript
Write-Host "🔨 构建TypeScript项目..." -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 构建失败" -ForegroundColor Red
    exit 1
}

# 修复JSON导入
Write-Host "🔧 修复JSON导入..." -ForegroundColor Yellow
$indexPath = "dist\utils\index.js"
if (Test-Path $indexPath) {
    $content = Get-Content $indexPath -Raw
    $content = $content -replace 'import province from "\.\.\/constans\/province\.json";', 'import province from "../constans/province.json" with { type: "json" };'
    $content = $content -replace 'import license from "\.\.\/constans\/licenseName\.json";', 'import license from "../constans/licenseName.json" with { type: "json" };'
    Set-Content $indexPath $content -Encoding UTF8
}

Write-Host "✅ 构建完成！" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 使用方法:" -ForegroundColor Cyan
Write-Host "  npm run start" -ForegroundColor White
Write-Host "  或者: node dist/index.js" -ForegroundColor White
Write-Host ""
