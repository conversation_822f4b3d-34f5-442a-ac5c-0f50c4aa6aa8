const require_chunk = require('./shared/chunk-qZFfknuJ.cjs');
const require_src = require('./shared/src-DU3H36fs.cjs');
const require_parse_ast_index = require('./shared/parse-ast-index-CfcTAL_S.cjs');
require('./shared/filter-index-DblXSw9s.cjs');
const node_worker_threads = require_chunk.__toESM(require("node:worker_threads"));

//#region src/parallel-plugin-worker.ts
const { registryId, pluginInfos, threadNumber } = node_worker_threads.workerData;
(async () => {
	try {
		const plugins = await Promise.all(pluginInfos.map(async (pluginInfo) => {
			const pluginModule = await import(pluginInfo.fileUrl);
			const definePluginImpl = pluginModule.default;
			const plugin = await definePluginImpl(pluginInfo.options, { threadNumber });
			return {
				index: pluginInfo.index,
				plugin: require_src.bindingifyPlugin(
					plugin,
					{},
					{},
					// TODO need to find a way to share pluginContextData
					new require_src.PluginContextData(),
					[],
					() => {},
					"info",
					// TODO: support this.meta.watchMode
					false
)
			};
		}));
		(0, require_parse_ast_index.import_binding.registerPlugins)(registryId, plugins);
		node_worker_threads.parentPort.postMessage({ type: "success" });
	} catch (error) {
		node_worker_threads.parentPort.postMessage({
			type: "error",
			error
		});
	} finally {
		node_worker_threads.parentPort.unref();
	}
})();

//#endregion