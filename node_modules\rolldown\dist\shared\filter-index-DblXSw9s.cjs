"use strict";

//#region src/utils/misc.ts
function arraify(value) {
	return Array.isArray(value) ? value : [value];
}
function isNullish(value) {
	return value === null || value === void 0;
}
function isPromiseLike(value) {
	return value && (typeof value === "object" || typeof value === "function") && typeof value.then === "function";
}
function unimplemented(info) {
	if (info) throw new Error(`unimplemented: ${info}`);
	throw new Error("unimplemented");
}
function unreachable(info) {
	if (info) throw new Error(`unreachable: ${info}`);
	throw new Error("unreachable");
}
function unsupported(info) {
	throw new Error(`UNSUPPORTED: ${info}`);
}
function noop(..._args) {}

//#endregion
//#region src/plugin/with-filter.ts
function withFilterImpl(pluginOption, filterObjectList) {
	if (isPromiseLike(pluginOption)) return pluginOption.then((p) => withFilter(p, filterObjectList));
	if (pluginOption == false || pluginOption == null) return pluginOption;
	if (Array.isArray(pluginOption)) return pluginOption.map((p) => withFilter(p, filterObjectList));
	let plugin = pluginOption;
	let filterObjectIndex = findMatchedFilterObject(plugin.name, filterObjectList);
	if (filterObjectIndex === -1) return plugin;
	let filterObject = filterObjectList[filterObjectIndex];
	Object.keys(plugin).forEach((key) => {
		switch (key) {
			case "transform":
			case "resolveId":
			case "load":
				if (!plugin[key]) return;
				if (typeof plugin[key] === "object") plugin[key].filter = filterObject[key] ?? plugin[key].filter;
				else plugin[key] = {
					handler: plugin[key],
					filter: filterObject[key]
				};
				break;
			default: break;
		}
	});
	return plugin;
}
function withFilter(pluginOption, filterObject) {
	return withFilterImpl(pluginOption, arraify(filterObject));
}
function findMatchedFilterObject(pluginName, overrideFilterObjectList) {
	if (overrideFilterObjectList.length === 1 && overrideFilterObjectList[0].pluginNamePattern === void 0) return 0;
	for (let i = 0; i < overrideFilterObjectList.length; i++) for (let j = 0; j < (overrideFilterObjectList[i].pluginNamePattern ?? []).length; j++) {
		let pattern = overrideFilterObjectList[i].pluginNamePattern[j];
		if (typeof pattern === "string" && pattern === pluginName) return i;
		else if (pattern instanceof RegExp && pattern.test(pluginName)) return i;
	}
	return -1;
}

//#endregion
//#region src/filter-index.ts
var And = class {
	kind;
	args;
	constructor(...args) {
		if (args.length === 0) throw new Error("`And` expects at least one operand");
		this.args = args;
		this.kind = "and";
	}
};
var Or = class {
	kind;
	args;
	constructor(...args) {
		if (args.length === 0) throw new Error("`Or` expects at least one operand");
		this.args = args;
		this.kind = "or";
	}
};
var Not = class {
	kind;
	expr;
	constructor(expr) {
		this.expr = expr;
		this.kind = "not";
	}
};
var Id = class {
	kind;
	pattern;
	constructor(pattern) {
		this.pattern = pattern;
		this.kind = "id";
	}
};
var ModuleType = class {
	kind;
	pattern;
	constructor(pattern) {
		this.pattern = pattern;
		this.kind = "moduleType";
	}
};
var Code = class {
	kind;
	pattern;
	constructor(expr) {
		this.pattern = expr;
		this.kind = "code";
	}
};
var Include = class {
	kind;
	expr;
	constructor(expr) {
		this.expr = expr;
		this.kind = "include";
	}
};
var Exclude = class {
	kind;
	expr;
	constructor(expr) {
		this.expr = expr;
		this.kind = "exclude";
	}
};
function and(...args) {
	return new And(...args);
}
function or(...args) {
	return new Or(...args);
}
function not(expr) {
	return new Not(expr);
}
function id(pattern) {
	return new Id(pattern);
}
function moduleType(pattern) {
	return new ModuleType(pattern);
}
function code(pattern) {
	return new Code(pattern);
}
function include(expr) {
	return new Include(expr);
}
function exclude(expr) {
	return new Exclude(expr);
}

//#endregion
Object.defineProperty(exports, 'And', {
  enumerable: true,
  get: function () {
    return And;
  }
});
Object.defineProperty(exports, 'and', {
  enumerable: true,
  get: function () {
    return and;
  }
});
Object.defineProperty(exports, 'arraify', {
  enumerable: true,
  get: function () {
    return arraify;
  }
});
Object.defineProperty(exports, 'code', {
  enumerable: true,
  get: function () {
    return code;
  }
});
Object.defineProperty(exports, 'exclude', {
  enumerable: true,
  get: function () {
    return exclude;
  }
});
Object.defineProperty(exports, 'id', {
  enumerable: true,
  get: function () {
    return id;
  }
});
Object.defineProperty(exports, 'include', {
  enumerable: true,
  get: function () {
    return include;
  }
});
Object.defineProperty(exports, 'isNullish', {
  enumerable: true,
  get: function () {
    return isNullish;
  }
});
Object.defineProperty(exports, 'moduleType', {
  enumerable: true,
  get: function () {
    return moduleType;
  }
});
Object.defineProperty(exports, 'noop', {
  enumerable: true,
  get: function () {
    return noop;
  }
});
Object.defineProperty(exports, 'not', {
  enumerable: true,
  get: function () {
    return not;
  }
});
Object.defineProperty(exports, 'or', {
  enumerable: true,
  get: function () {
    return or;
  }
});
Object.defineProperty(exports, 'unimplemented', {
  enumerable: true,
  get: function () {
    return unimplemented;
  }
});
Object.defineProperty(exports, 'unreachable', {
  enumerable: true,
  get: function () {
    return unreachable;
  }
});
Object.defineProperty(exports, 'unsupported', {
  enumerable: true,
  get: function () {
    return unsupported;
  }
});
Object.defineProperty(exports, 'withFilter', {
  enumerable: true,
  get: function () {
    return withFilter;
  }
});