import { BindingBuildImportAnalysisPluginConfig, BindingBuiltinPluginName, BindingDynamicImportVarsPluginConfig, BindingGlobImportPluginConfig, BindingHmrOutput, BindingHookResolveIdExtraArgs, BindingIsolatedDeclarationPluginConfig, BindingJsonPluginConfig, BindingManifestPluginConfig, BindingMfManifest, BindingMinifyOptions, BindingModuleFederationPluginOption, BindingModulePreloadPolyfillPluginConfig, BindingRemote, BindingRenderedChunk, BindingReportPluginConfig, BindingTransformHookExtraArgs, BindingViteResolvePluginConfig, BindingWatcherEvent, ParserOptions, PreRenderedChunk, TransformOptions } from "./binding.d-BIqDyMRC.cjs";
import { Program } from "@oxc-project/types";

//#region src/log/logging.d.ts
type LogLevel = "info" | "debug" | "warn";
type LogLevelOption = LogLevel | "silent";
type LogLevelWithError = LogLevel | "error";
interface RollupLog {
	binding?: string;
	cause?: unknown;
	code?: string;
	exporter?: string;
	frame?: string;
	hook?: string;
	id?: string;
	ids?: string[];
	loc?: {
		column: number
		file?: string
		line: number
	};
	message: string;
	meta?: any;
	names?: string[];
	plugin?: string;
	pluginCode?: unknown;
	pos?: number;
	reexporter?: string;
	stack?: string;
	url?: string;
}
type RollupLogWithString = RollupLog | string;
interface RollupError extends RollupLog {
	name?: string;
	stack?: string;
	watchFiles?: string[];
}
type LogOrStringHandler = (level: LogLevelWithError, log: RollupLogWithString) => void;

//#endregion
//#region src/types/misc.d.ts
type SourcemapPathTransformOption = (relativeSourcePath: string, sourcemapPath: string) => string;
type SourcemapIgnoreListOption = (relativeSourcePath: string, sourcemapPath: string) => boolean;

//#endregion
//#region src/utils/asset-source.d.ts
type AssetSource = string | Uint8Array;

//#endregion
//#region src/types/rolldown-output.d.ts
interface OutputAsset {
	type: "asset";
	fileName: string;
	/** @deprecated Use "originalFileNames" instead. */
	originalFileName: string | null;
	originalFileNames: string[];
	source: AssetSource;
	/** @deprecated Use "names" instead. */
	name: string | undefined;
	names: string[];
}
interface SourceMap {
	file: string;
	mappings: string;
	names: string[];
	sources: string[];
	sourcesContent: string[];
	version: number;
	debugId?: string;
	x_google_ignoreList?: number[];
	toString(): string;
	toUrl(): string;
}
interface RenderedModule {
	readonly code: string | null;
	renderedLength: number;
	renderedExports: string[];
}
interface RenderedChunk extends Omit<BindingRenderedChunk, "modules"> {
	modules: {
		[id: string]: RenderedModule
	};
	name: string;
	isEntry: boolean;
	isDynamicEntry: boolean;
	facadeModuleId: string | null;
	moduleIds: Array<string>;
	exports: Array<string>;
	fileName: string;
	imports: Array<string>;
	dynamicImports: Array<string>;
}
interface OutputChunk {
	type: "chunk";
	code: string;
	name: string;
	isEntry: boolean;
	exports: string[];
	fileName: string;
	modules: {
		[id: string]: RenderedModule
	};
	imports: string[];
	dynamicImports: string[];
	facadeModuleId: string | null;
	isDynamicEntry: boolean;
	moduleIds: string[];
	map: SourceMap | null;
	sourcemapFileName: string | null;
	preliminaryFileName: string;
}
interface RolldownOutput {
	output: [OutputChunk, ...(OutputChunk | OutputAsset)[]];
}

//#endregion
//#region src/types/utils.d.ts
type MaybePromise<T> = T | Promise<T>;
type NullValue<T = void> = T | undefined | null | void;
type PartialNull<T> = { [P in keyof T] : T[P] | null };
type MakeAsync<Function_> = Function_ extends (this: infer This, ...parameters: infer Arguments) => infer Return ? (this: This, ...parameters: Arguments) => Return | Promise<Return> : never;
type MaybeArray<T> = T | T[];
type StringOrRegExp = string | RegExp;

//#endregion
//#region src/options/output-options.d.ts
type ModuleFormat = "es" | "cjs" | "esm" | "module" | "commonjs" | "iife" | "umd" | "experimental-app";
type AddonFunction = (chunk: RenderedChunk) => string | Promise<string>;
type ChunkFileNamesFunction = (chunkInfo: PreRenderedChunk) => string;
interface PreRenderedAsset {
	names: string[];
	originalFileNames: string[];
	source: string | Uint8Array;
	type: "asset";
}
type AssetFileNamesFunction = (chunkInfo: PreRenderedAsset) => string;
type GlobalsFunction = (name: string) => string;
type ESTarget = "es6" | "es2015" | "es2016" | "es2017" | "es2018" | "es2019" | "es2020" | "es2021" | "es2022" | "es2023" | "es2024" | "esnext";
type MinifyOptions = BindingMinifyOptions;
interface OutputOptions {
	dir?: string;
	file?: string;
	exports?: "auto" | "named" | "default" | "none";
	hashCharacters?: "base64" | "base36" | "hex";
	/**
	* Expected format of generated code.
	* - `'es'`, `'esm'` and `'module'` are the same format, all stand for ES module.
	* - `'cjs'` and `'commonjs'` are the same format, all stand for CommonJS module.
	* - `'iife'` stands for [Immediately Invoked Function Expression](https://developer.mozilla.org/en-US/docs/Glossary/IIFE).
	* - `'umd'` stands for [Universal Module Definition](https://github.com/umdjs/umd).
	*
	* @default 'esm'
	*/
	format?: ModuleFormat;
	sourcemap?: boolean | "inline" | "hidden";
	sourcemapDebugIds?: boolean;
	sourcemapIgnoreList?: boolean | SourcemapIgnoreListOption;
	sourcemapPathTransform?: SourcemapPathTransformOption;
	banner?: string | AddonFunction;
	footer?: string | AddonFunction;
	intro?: string | AddonFunction;
	outro?: string | AddonFunction;
	extend?: boolean;
	esModule?: boolean | "if-default-prop";
	assetFileNames?: string | AssetFileNamesFunction;
	entryFileNames?: string | ChunkFileNamesFunction;
	chunkFileNames?: string | ChunkFileNamesFunction;
	cssEntryFileNames?: string | ChunkFileNamesFunction;
	cssChunkFileNames?: string | ChunkFileNamesFunction;
	sanitizeFileName?: boolean | ((name: string) => string);
	minify?: boolean | "dce-only" | MinifyOptions;
	name?: string;
	globals?: Record<string, string> | GlobalsFunction;
	externalLiveBindings?: boolean;
	inlineDynamicImports?: boolean;
	/**
	* Allows you to do advanced chunking. Use it to reduce the number of common chunks or split out a chunk that hardly changes to obtain better caching.
	*/
	advancedChunks?: {
		/**
		* - Type: `number`
		*
		* Global fallback of [`{group}.minSize`](#advancedchunks-groups-minsize), if it's not specified in the group.
		*/
		minSize?: number
		/**
		* - Type: `number`
		*
		* Global fallback of [`{group}.maxSize`](#advancedchunks-groups-maxsize), if it's not specified in the group.
		*/
		maxSize?: number
		/**
		* - Type: `number`
		*
		* Global fallback of [`{group}.maxModuleSize`](#advancedchunks-groups-maxmodulesize), if it's not specified in the group.
		*/
		maxModuleSize?: number
		/**
		* - Type: `number`
		*
		* Global fallback of [`{group}.minModuleSize`](#advancedchunks-groups-minmodulesize), if it's not specified in the group.
		*/
		minModuleSize?: number
		/**
		* - Type: `number`
		*
		* Global fallback of [`{group}.minShareCount`](#advancedchunks-groups-minsharecount), if it's not specified in the group.
		*/
		minShareCount?: number
		/**
		* Groups to be used for advanced chunking.
		*/
		groups?: {
			/**
			* - Type: `string`
			*
			* Name of the group. It will be also used as the name of the chunk and replaced the `[name]` placeholder in the `chunkFileNames` option.
			*/
			name: string
			/**
			* - Type: `string | RegExp`
			*
			* Controls which modules are captured in this group.
			*
			* If `test` is a string, the module whose id contains the string will be captured.
			* If `test` is a regular expression, the module whose id matches the regular expression will be captured.
			* if `test` is empty, any module will be considered as matched.
			*/
			test?: StringOrRegExp
			/**
			* - Type: `number`
			*
			* Priority of the group. Group with higher priority will be chosen first to match modules and create chunks. When converting the group to a chunk, modules of that group will be removed from other groups.
			*
			* If two groups have the same priority, the group whose index is smaller will be chosen.
			*/
			priority?: number
			/**
			* - Type: `number`
			* - Default: `0`
			*
			* Minimum size of the desired chunk. If accumulated size of captured modules is smaller than this value, this group will be ignored.
			*/
			minSize?: number
			/**
			* - Type: `number`
			* - Default: `1`
			*
			* Controls if a module should be captured based on how many entry chunks reference it.
			*/
			minShareCount?: number
			/**
			* - Type: `number`
			* - Default: `Infinity`
			*
			* If final size of this group is larger than this value, this group will be spit into multiple groups that each has size closed to this value.
			*/
			maxSize?: number
			/**
			* - Type: `number`
			* - Default: `Infinity`
			*
			* Controls a module could only be captured if its size is smaller or equal than this value.
			*/
			maxModuleSize?: number
			/**
			* - Type: `number`
			* - Default: `0`
			*
			* Controls a module could only be captured if its size is larger or equal than this value.
			*/
			minModuleSize?: number
		}[]
	};
	/**
	* Control comments in the output.
	*
	* - `none`: no comments
	* - `preserve-legal`: preserve comments that contain `@license`, `@preserve` or starts with `//!` `/*!`
	*/
	comments?: "none" | "preserve-legal";
	plugins?: RolldownOutputPluginOption;
	polyfillRequire?: boolean;
	target?: ESTarget;
	hoistTransitiveImports?: false;
}

//#endregion
//#region src/api/build.d.ts
interface BuildOptions extends InputOptions {
	/**
	* Write the output to the file system
	*
	* @default true
	*/
	write?: boolean;
	output?: OutputOptions;
}
declare function build(options: BuildOptions): Promise<RolldownOutput>;
/**
* Build multiple outputs __sequentially__.
*/
/**
* Build multiple outputs __sequentially__.
*/
declare function build(options: BuildOptions[]): Promise<RolldownOutput[]>;

//#endregion
//#region src/api/rolldown/rolldown-build.d.ts
declare class RolldownBuild {
	#private;
	constructor(inputOptions: InputOptions);
	get closed(): boolean;
	generate(outputOptions?: OutputOptions): Promise<RolldownOutput>;
	write(outputOptions?: OutputOptions): Promise<RolldownOutput>;
	close(): Promise<void>;
	[Symbol.asyncDispose](): Promise<void>;
	generateHmrPatch(changedFiles: string[]): Promise<BindingHmrOutput | undefined>;
	hmrInvalidate(file: string, firstInvalidatedBy?: string): Promise<BindingHmrOutput | undefined>;
	get watchFiles(): string[];
}

//#endregion
//#region src/api/rolldown/index.d.ts
declare const rolldown: (input: InputOptions) => Promise<RolldownBuild>;

//#endregion
//#region src/options/watch-options.d.ts
interface WatchOptions extends InputOptions {
	output?: OutputOptions | OutputOptions[];
}

//#endregion
//#region src/api/watch/watch-emitter.d.ts
type WatcherEvent = "close" | "event" | "restart" | "change";
type ChangeEvent$1 = "create" | "update" | "delete";
type RolldownWatcherEvent = {
	code: "START"
} | {
	code: "BUNDLE_START"
} | {
	code: "BUNDLE_END"
	duration: number
	output: readonly string[]
} | {
	code: "END"
} | {
	code: "ERROR"
	error: Error
};
declare class WatcherEmitter {
	listeners: Map<WatcherEvent, Array<(...parameters: any[]) => MaybePromise<void>>>;
	timer: any;
	constructor();
	on(event: "change", listener: (id: string, change: {
		event: ChangeEvent$1
	}) => MaybePromise<void>): this;
	on(event: "event", listener: (data: RolldownWatcherEvent) => MaybePromise<void>): this;
	on(event: "restart" | "close", listener: () => MaybePromise<void>): this;
	off(event: WatcherEvent, listener: (...parameters: any[]) => MaybePromise<void>): this;
	onEvent(event: BindingWatcherEvent): Promise<void>;
	close(): Promise<void>;
}
type RolldownWatcher = WatcherEmitter;

//#endregion
//#region src/api/watch/index.d.ts
declare const watch: (input: WatchOptions | WatchOptions[]) => RolldownWatcher;

//#endregion
//#region src/log/log-handler.d.ts
type LoggingFunction = (log: RollupLog | string | (() => RollupLog | string)) => void;
type LoggingFunctionWithPosition = (log: RollupLog | string | (() => RollupLog | string), pos?: number | {
	column: number
	line: number
}) => void;
type WarningHandlerWithDefault = (warning: RollupLog, defaultHandler: LoggingFunction) => void;

//#endregion
//#region src/options/normalized-input-options.d.ts
interface NormalizedInputOptions {
	input: string[] | Record<string, string>;
	cwd: string | undefined;
	platform: InputOptions["platform"];
	shimMissingExports: boolean;
}

//#endregion
//#region src/options/normalized-output-options.d.ts
type InternalModuleFormat = "es" | "cjs" | "iife" | "umd" | "app";
interface NormalizedOutputOptions {
	name: string | undefined;
	file: string | undefined;
	dir: string | undefined;
	entryFileNames: string | ChunkFileNamesFunction;
	chunkFileNames: string | ChunkFileNamesFunction;
	assetFileNames: string | AssetFileNamesFunction;
	format: InternalModuleFormat;
	exports: NonNullable<OutputOptions["exports"]>;
	sourcemap: boolean | "inline" | "hidden";
	cssEntryFileNames: string | ChunkFileNamesFunction;
	cssChunkFileNames: string | ChunkFileNamesFunction;
	inlineDynamicImports: boolean;
	externalLiveBindings: boolean;
	banner: AddonFunction;
	footer: AddonFunction;
	intro: AddonFunction;
	outro: AddonFunction;
	esModule: boolean | "if-default-prop";
	extend: boolean;
	globals: Record<string, string> | GlobalsFunction;
	hashCharacters: "base64" | "base36" | "hex";
	sourcemapDebugIds: boolean;
	sourcemapIgnoreList: SourcemapIgnoreListOption;
	sourcemapPathTransform: SourcemapPathTransformOption | undefined;
	minify: false | BindingMinifyOptions;
	comments: "none" | "preserve-legal";
	polyfillRequire: boolean;
	plugins: RolldownPlugin[];
}

//#endregion
//#region src/filter-index.d.ts
type FilterExpressionKind = FilterExpression["kind"];
type FilterExpression = And | Or | Not | Id | ModuleType$1 | Code | Include | Exclude$1;
type TopLevelFilterExpression = Include | Exclude$1;
declare class And {
	kind: "and";
	args: FilterExpression[];
	constructor(...args: FilterExpression[]);
}
declare class Or {
	kind: "or";
	args: FilterExpression[];
	constructor(...args: FilterExpression[]);
}
declare class Not {
	kind: "not";
	expr: FilterExpression;
	constructor(expr: FilterExpression);
}
declare class Id {
	kind: "id";
	pattern: StringOrRegExp;
	constructor(pattern: StringOrRegExp);
}
declare class ModuleType$1 {
	kind: "moduleType";
	pattern: ModuleType;
	constructor(pattern: ModuleType);
}
declare class Code {
	kind: "code";
	pattern: StringOrRegExp;
	constructor(expr: StringOrRegExp);
}
declare class Include {
	kind: "include";
	expr: FilterExpression;
	constructor(expr: FilterExpression);
}
declare class Exclude$1 {
	kind: "exclude";
	expr: FilterExpression;
	constructor(expr: FilterExpression);
}
declare function and(...args: FilterExpression[]): And;
declare function or(...args: FilterExpression[]): Or;
declare function not(expr: FilterExpression): Not;
declare function id(pattern: StringOrRegExp): Id;
declare function moduleType(pattern: ModuleType): ModuleType$1;
declare function code(pattern: StringOrRegExp): Code;
declare function include(expr: FilterExpression): Include;
declare function exclude(expr: FilterExpression): Exclude$1;

//#endregion
//#region src/plugin/hook-filter.d.ts
type GeneralHookFilter<Value = StringOrRegExp> = MaybeArray<Value> | {
	include?: MaybeArray<Value>
	exclude?: MaybeArray<Value>
};
interface FormalModuleTypeFilter {
	include?: ModuleType[];
}
type ModuleTypeFilter = ModuleType[] | FormalModuleTypeFilter;
interface HookFilter {
	/**
	* This filter is used to do a pre-test to determine whether the hook should be called.
	*
	* @example
	* Include all `id`s that contain `node_modules` in the path.
	* ```js
	* { id: '**'+'/node_modules/**' }
	* ```
	* @example
	* Include all `id`s that contain `node_modules` or `src` in the path.
	* ```js
	* { id: ['**'+'/node_modules/**', '**'+'/src/**'] }
	* ```
	* @example
	* Include all `id`s that start with `http`
	* ```js
	* { id: /^http/ }
	* ```
	* @example
	* Exclude all `id`s that contain `node_modules` in the path.
	* ```js
	* { id: { exclude: '**'+'/node_modules/**' } }
	* ```
	* @example
	* Formal pattern to define includes and excludes.
	* ```
	* { id : {
	*   include: ['**'+'/foo/**', /bar/],
	*   exclude: ['**'+'/baz/**', /qux/]
	* }}
	* ```
	*/
	id?: GeneralHookFilter;
	moduleType?: ModuleTypeFilter;
	code?: GeneralHookFilter;
}
type TUnionWithTopLevelFilterExpressionArray<T> = T | TopLevelFilterExpression[];

//#endregion
//#region src/plugin/minimal-plugin-context.d.ts
interface PluginContextMeta {
	rollupVersion: string;
	rolldownVersion: string;
	watchMode: boolean;
}
interface MinimalPluginContext {
	readonly pluginName: string;
	error: (e: RollupError | string) => never;
	info: LoggingFunction;
	warn: LoggingFunction;
	debug: LoggingFunction;
	meta: PluginContextMeta;
}

//#endregion
//#region src/plugin/parallel-plugin.d.ts
type ParallelPlugin = {
	/** @internal */
	_parallel: {
		fileUrl: string
		options: unknown
	}
};
type DefineParallelPluginResult<Options> = (options: Options) => ParallelPlugin;
declare function defineParallelPlugin<Options>(pluginPath: string): DefineParallelPluginResult<Options>;

//#endregion
//#region src/types/module-info.d.ts
interface ModuleInfo extends ModuleOptions {
	/**
	*  Unsupported at rolldown
	*/
	ast: any;
	code: string | null;
	id: string;
	importers: string[];
	dynamicImporters: string[];
	importedIds: string[];
	dynamicallyImportedIds: string[];
	exports: string[];
	isEntry: boolean;
}

//#endregion
//#region src/plugin/plugin-context.d.ts
interface EmittedAsset {
	type: "asset";
	name?: string;
	fileName?: string;
	originalFileName?: string | null;
	source: AssetSource;
}
interface EmittedChunk {
	type: "chunk";
	name?: string;
	fileName?: string;
	id: string;
	importer?: string;
}
type EmittedFile = EmittedAsset | EmittedChunk;
interface PluginContextResolveOptions {
	skipSelf?: boolean;
	custom?: CustomPluginOptions;
}
type GetModuleInfo = (moduleId: string) => ModuleInfo | null;
interface PluginContext extends MinimalPluginContext {
	emitFile(file: EmittedFile): string;
	getFileName(referenceId: string): string;
	getModuleIds(): IterableIterator<string>;
	getModuleInfo: GetModuleInfo;
	addWatchFile(id: string): void;
	load(options: {
		id: string
		resolveDependencies?: boolean
	} & Partial<PartialNull<ModuleOptions>>): Promise<ModuleInfo>;
	parse(input: string, options?: ParserOptions | undefined | null): Program;
	resolve(source: string, importer?: string, options?: PluginContextResolveOptions): Promise<ResolvedId | null>;
}

//#endregion
//#region src/plugin/transform-plugin-context.d.ts
interface TransformPluginContext extends PluginContext {
	debug: LoggingFunctionWithPosition;
	info: LoggingFunctionWithPosition;
	warn: LoggingFunctionWithPosition;
	error(e: RollupError | string, pos?: number | {
		column: number
		line: number
	}): never;
	getCombinedSourcemap(): SourceMap;
}

//#endregion
//#region src/types/rolldown-options.d.ts
interface RolldownOptions extends InputOptions {
	output?: OutputOptions | OutputOptions[];
}

//#endregion
//#region src/types/config-export.d.ts
/**
* Type for `default export` of `rolldown.config.js` file.
*/
/**
* Type for `default export` of `rolldown.config.js` file.
*/
type ConfigExport = RolldownOptions | RolldownOptions[];

//#endregion
//#region src/types/module-side-effects.d.ts
interface ModuleSideEffectsRule {
	test?: RegExp;
	external?: boolean;
	sideEffects: boolean;
}
type ModuleSideEffectsOption = boolean | ModuleSideEffectsRule[] | ((id: string, isResolved: boolean) => boolean | undefined) | "no-external";
type TreeshakingOptions = {
	moduleSideEffects?: ModuleSideEffectsOption
	annotations?: boolean
	manualPureFunctions?: string[]
	unknownGlobalSideEffects?: boolean
} | boolean;

//#endregion
//#region src/types/output-bundle.d.ts
interface OutputBundle {
	[fileName: string]: OutputAsset | OutputChunk;
}

//#endregion
//#region src/types/sourcemap.d.ts
interface ExistingRawSourceMap {
	file?: string | null;
	mappings: string;
	names?: string[];
	sources?: (string | null)[];
	sourcesContent?: (string | null)[];
	sourceRoot?: string;
	version?: number;
	x_google_ignoreList?: number[];
}
type SourceMapInput = ExistingRawSourceMap | string | null;

//#endregion
//#region src/utils/define-config.d.ts
declare function defineConfig(config: RolldownOptions): RolldownOptions;
declare function defineConfig(config: RolldownOptions[]): RolldownOptions[];
declare function defineConfig(config: ConfigExport): ConfigExport;

//#endregion
//#region src/index.d.ts
declare const VERSION: string;

//#endregion
//#region src/builtin-plugin/constructors.d.ts
declare class BuiltinPlugin {
	name: BindingBuiltinPluginName;
	_options?: unknown;
	constructor(name: BindingBuiltinPluginName, _options?: unknown);
}
declare function modulePreloadPolyfillPlugin(config?: BindingModulePreloadPolyfillPluginConfig): BuiltinPlugin;
declare function dynamicImportVarsPlugin(config?: BindingDynamicImportVarsPluginConfig): BuiltinPlugin;
declare function importGlobPlugin(config?: BindingGlobImportPluginConfig): BuiltinPlugin;
declare function reportPlugin(config?: BindingReportPluginConfig): BuiltinPlugin;
declare function manifestPlugin(config?: BindingManifestPluginConfig): BuiltinPlugin;
declare function wasmHelperPlugin(): BuiltinPlugin;
declare function wasmFallbackPlugin(): BuiltinPlugin;
declare function loadFallbackPlugin(): BuiltinPlugin;
declare function jsonPlugin(config?: BindingJsonPluginConfig): BuiltinPlugin;
declare function buildImportAnalysisPlugin(config: BindingBuildImportAnalysisPluginConfig): BuiltinPlugin;
declare function viteResolvePlugin(config: BindingViteResolvePluginConfig): BuiltinPlugin;
type ModuleFederationPluginOption = Omit<BindingModuleFederationPluginOption, "remotes"> & {
	remotes?: Record<string, string | BindingRemote>
	manifest?: boolean | BindingMfManifest
};
declare function moduleFederationPlugin(config: ModuleFederationPluginOption): BuiltinPlugin;
declare function isolatedDeclarationPlugin(config?: BindingIsolatedDeclarationPluginConfig): BuiltinPlugin;

//#endregion
//#region src/constants/plugin.d.ts
declare const ENUMERATED_INPUT_PLUGIN_HOOK_NAMES: readonly ["options", "buildStart", "resolveId", "load", "transform", "moduleParsed", "buildEnd", "onLog", "resolveDynamicImport", "closeBundle", "closeWatcher", "watchChange"];
declare const ENUMERATED_OUTPUT_PLUGIN_HOOK_NAMES: readonly ["augmentChunkHash", "outputOptions", "renderChunk", "renderStart", "renderError", "writeBundle", "generateBundle"];
declare const ENUMERATED_PLUGIN_HOOK_NAMES: [...typeof ENUMERATED_INPUT_PLUGIN_HOOK_NAMES, ...typeof ENUMERATED_OUTPUT_PLUGIN_HOOK_NAMES, "footer", "banner", "intro", "outro"];
type EnumeratedPluginHookNames = typeof ENUMERATED_PLUGIN_HOOK_NAMES;
/**
* Names of all hooks in a `Plugin` object. Does not include `name` and `api`, since they are not hooks.
*/
/**
* Names of all hooks in a `Plugin` object. Does not include `name` and `api`, since they are not hooks.
*/
type PluginHookNames = EnumeratedPluginHookNames[number];
/**
* Names of all defined hooks. It's like
* ```ts
* type DefinedHookNames = {
*   options: 'options',
*   buildStart: 'buildStart',
*   ...
* }
* ```
*/
/**
* Names of all defined hooks. It's like
* ```ts
* type DefinedHookNames = {
*   options: 'options',
*   buildStart: 'buildStart',
*   ...
* }
* ```
*/
type DefinedHookNames = { readonly [K in PluginHookNames] : K };
/**
* Names of all defined hooks. It's like
* ```js
* const DEFINED_HOOK_NAMES ={
*   options: 'options',
*   buildStart: 'buildStart',
*   ...
* }
* ```
*/
/**
* Names of all defined hooks. It's like
* ```js
* const DEFINED_HOOK_NAMES ={
*   options: 'options',
*   buildStart: 'buildStart',
*   ...
* }
* ```
*/
declare const DEFINED_HOOK_NAMES: DefinedHookNames;

//#endregion
//#region src/plugin/with-filter.d.ts
type OverrideFilterObject = {
	transform?: HookFilterExtension<"transform">["filter"]
	resolveId?: HookFilterExtension<"resolveId">["filter"]
	load?: HookFilterExtension<"load">["filter"]
	pluginNamePattern?: StringOrRegExp[]
};
declare function withFilter<
	A,
	T extends RolldownPluginOption<A>
>(pluginOption: T, filterObject: OverrideFilterObject | OverrideFilterObject[]): T;

//#endregion
//#region src/plugin/index.d.ts
type ModuleSideEffects = boolean | "no-treeshake" | null;
type ModuleType = "js" | "jsx" | "ts" | "tsx" | "json" | "text" | "base64" | "dataurl" | "binary" | "empty" | (string & {});
type ImportKind = BindingHookResolveIdExtraArgs["kind"];
interface CustomPluginOptions {
	[plugin: string]: any;
}
interface ModuleOptions {
	moduleSideEffects: ModuleSideEffects;
	meta: CustomPluginOptions;
	invalidate?: boolean;
}
interface ResolvedId extends ModuleOptions {
	external: boolean | "absolute";
	id: string;
}
interface PartialResolvedId extends Partial<PartialNull<ModuleOptions>> {
	external?: boolean | "absolute" | "relative";
	id: string;
}
interface SourceDescription extends Partial<PartialNull<ModuleOptions>> {
	code: string;
	map?: SourceMapInput;
	moduleType?: ModuleType;
}
interface ResolveIdExtraOptions {
	custom?: CustomPluginOptions;
	isEntry: boolean;
	kind: "import" | "dynamic-import" | "require-call";
}
type ResolveIdResult = string | NullValue | false | PartialResolvedId;
type LoadResult = NullValue | string | SourceDescription;
type TransformResult = NullValue | string | Partial<SourceDescription>;
type RenderedChunkMeta = {
	chunks: Record<string, RenderedChunk>
};
interface FunctionPluginHooks {
	[DEFINED_HOOK_NAMES.onLog]: (this: MinimalPluginContext, level: LogLevel, log: RollupLog) => NullValue | boolean;
	[DEFINED_HOOK_NAMES.options]: (this: MinimalPluginContext, options: InputOptions) => NullValue | InputOptions;
	[DEFINED_HOOK_NAMES.outputOptions]: (this: MinimalPluginContext, options: OutputOptions) => NullValue | OutputOptions;
	[DEFINED_HOOK_NAMES.buildStart]: (this: PluginContext, options: NormalizedInputOptions) => void;
	[DEFINED_HOOK_NAMES.resolveId]: (this: PluginContext, source: string, importer: string | undefined, extraOptions: ResolveIdExtraOptions) => ResolveIdResult;
	/**
	* @deprecated
	* This hook is only for rollup plugin compatibility. Please use `resolveId` instead.
	*/
	[DEFINED_HOOK_NAMES.resolveDynamicImport]: (this: PluginContext, source: string, importer: string | undefined) => ResolveIdResult;
	[DEFINED_HOOK_NAMES.load]: (this: PluginContext, id: string) => MaybePromise<LoadResult>;
	[DEFINED_HOOK_NAMES.transform]: (this: TransformPluginContext, code: string, id: string, meta: BindingTransformHookExtraArgs & {
		moduleType: ModuleType
	}) => TransformResult;
	[DEFINED_HOOK_NAMES.moduleParsed]: (this: PluginContext, moduleInfo: ModuleInfo) => void;
	[DEFINED_HOOK_NAMES.buildEnd]: (this: PluginContext, err?: Error) => void;
	[DEFINED_HOOK_NAMES.renderStart]: (this: PluginContext, outputOptions: NormalizedOutputOptions, inputOptions: NormalizedInputOptions) => void;
	[DEFINED_HOOK_NAMES.renderChunk]: (this: PluginContext, code: string, chunk: RenderedChunk, outputOptions: NormalizedOutputOptions, meta: RenderedChunkMeta) => NullValue | string | {
		code: string
		map?: SourceMapInput
	};
	[DEFINED_HOOK_NAMES.augmentChunkHash]: (this: PluginContext, chunk: RenderedChunk) => string | void;
	[DEFINED_HOOK_NAMES.renderError]: (this: PluginContext, error: Error) => void;
	[DEFINED_HOOK_NAMES.generateBundle]: (this: PluginContext, outputOptions: NormalizedOutputOptions, bundle: OutputBundle, isWrite: boolean) => void;
	[DEFINED_HOOK_NAMES.writeBundle]: (this: PluginContext, outputOptions: NormalizedOutputOptions, bundle: OutputBundle) => void;
	[DEFINED_HOOK_NAMES.closeBundle]: (this: PluginContext) => void;
	[DEFINED_HOOK_NAMES.watchChange]: (this: PluginContext, id: string, event: {
		event: ChangeEvent
	}) => void;
	[DEFINED_HOOK_NAMES.closeWatcher]: (this: PluginContext) => void;
}
type ChangeEvent = "create" | "update" | "delete";
type PluginOrder = "pre" | "post" | null;
type ObjectHookMeta = {
	order?: PluginOrder
};
type ObjectHook<
	T,
	O = {}
> = T | ({
	handler: T
} & ObjectHookMeta & O);
type SyncPluginHooks = DefinedHookNames["augmentChunkHash" | "onLog" | "outputOptions"];
type AsyncPluginHooks = Exclude<keyof FunctionPluginHooks, SyncPluginHooks>;
type FirstPluginHooks = DefinedHookNames["load" | "resolveDynamicImport" | "resolveId"];
type SequentialPluginHooks = DefinedHookNames["augmentChunkHash" | "generateBundle" | "onLog" | "options" | "outputOptions" | "renderChunk" | "transform"];
type AddonHooks = DefinedHookNames["banner" | "footer" | "intro" | "outro"];
type OutputPluginHooks = DefinedHookNames["augmentChunkHash" | "generateBundle" | "outputOptions" | "renderChunk" | "renderError" | "renderStart" | "writeBundle"];
type ParallelPluginHooks = Exclude<keyof FunctionPluginHooks | AddonHooks, FirstPluginHooks | SequentialPluginHooks>;
type HookFilterExtension<K extends keyof FunctionPluginHooks> = K extends "transform" ? {
	filter?: TUnionWithTopLevelFilterExpressionArray<HookFilter>
} : K extends "load" ? {
	filter?: TUnionWithTopLevelFilterExpressionArray<Pick<HookFilter, "id">>
} : K extends "resolveId" ? {
	filter?: TUnionWithTopLevelFilterExpressionArray<{
		id?: GeneralHookFilter<RegExp>
	}>
} : K extends "renderChunk" ? {
	filter?: TUnionWithTopLevelFilterExpressionArray<Pick<HookFilter, "code">>
} : {};
type PluginHooks = { [K in keyof FunctionPluginHooks] : ObjectHook<K extends AsyncPluginHooks ? MakeAsync<FunctionPluginHooks[K]> : FunctionPluginHooks[K], HookFilterExtension<K> & (K extends ParallelPluginHooks ? {
	/**
	* @deprecated
	* this is only for rollup Plugin type compatibility.
	* hooks always work as `sequential: true`.
	*/
	sequential?: boolean
} : {})> };
type AddonHookFunction = (this: PluginContext, chunk: RenderedChunk) => string | Promise<string>;
type AddonHook = string | AddonHookFunction;
interface OutputPlugin extends Partial<{ [K in OutputPluginHooks] : PluginHooks[K] }>, Partial<{ [K in AddonHooks] : ObjectHook<AddonHook> }> {
	name: string;
}
interface Plugin<A = any> extends OutputPlugin, Partial<PluginHooks> {
	api?: A;
}
type RolldownPlugin<A = any> = Plugin<A> | BuiltinPlugin | ParallelPlugin;
type RolldownPluginOption<A = any> = MaybePromise<NullValue<RolldownPlugin<A>> | false | RolldownPluginOption[]>;
type RolldownOutputPlugin = OutputPlugin | BuiltinPlugin;
type RolldownOutputPluginOption = MaybePromise<NullValue<RolldownOutputPlugin> | false | RolldownOutputPluginOption[]>;

//#endregion
//#region src/options/generated/checks-options.d.ts
interface ChecksOptions {
	/**
	* Whether to emit warning when detecting circular dependency
	* @default false
	*/
	circularDependency?: boolean;
	/**
	* Whether to emit warning when detecting eval
	* @default true
	*/
	eval?: boolean;
	/**
	* Whether to emit warning when detecting missing global name
	* @default true
	*/
	missingGlobalName?: boolean;
	/**
	* Whether to emit warning when detecting missing name option for iife export
	* @default true
	*/
	missingNameOptionForIifeExport?: boolean;
	/**
	* Whether to emit warning when detecting mixed export
	* @default true
	*/
	mixedExport?: boolean;
	/**
	* Whether to emit warning when detecting unresolved entry
	* @default true
	*/
	unresolvedEntry?: boolean;
	/**
	* Whether to emit warning when detecting unresolved import
	* @default true
	*/
	unresolvedImport?: boolean;
	/**
	* Whether to emit warning when detecting filename conflict
	* @default true
	*/
	filenameConflict?: boolean;
	/**
	* Whether to emit warning when detecting common js variable in esm
	* @default true
	*/
	commonJsVariableInEsm?: boolean;
	/**
	* Whether to emit warning when detecting import is undefined
	* @default true
	*/
	importIsUndefined?: boolean;
	/**
	* Whether to emit warning when detecting configuration field conflict
	* @default true
	*/
	configurationFieldConflict?: boolean;
}

//#endregion
//#region src/options/input-options.d.ts
type InputOption = string | string[] | Record<string, string>;
type OxcTransformOption = Omit<TransformOptions, "sourceType" | "lang" | "cwd" | "sourcemap" | "jsx" | "define" | "inject" | "target">;
type ExternalOption = StringOrRegExp | StringOrRegExp[] | ((id: string, parentId: string | undefined, isResolved: boolean) => NullValue<boolean>);
type ModuleTypes = Record<string, "js" | "jsx" | "ts" | "tsx" | "json" | "text" | "base64" | "dataurl" | "binary" | "empty" | "css">;
interface JsxOptions {
	mode?: "classic" | "automatic" | "preserve";
	factory?: string;
	fragment?: string;
	importSource?: string;
	jsxImportSource?: string;
	refresh?: boolean;
	development?: boolean;
}
interface WatcherOptions {
	skipWrite?: boolean;
	buildDelay?: number;
	notify?: {
		pollInterval?: number
		compareContents?: boolean
	};
	include?: StringOrRegExp | StringOrRegExp[];
	exclude?: StringOrRegExp | StringOrRegExp[];
}
type MakeAbsoluteExternalsRelative = boolean | "ifRelativeSource";
type HmrOptions = boolean | {
	host?: string
	port?: number
	implement?: string
};
interface InputOptions {
	input?: InputOption;
	plugins?: RolldownPluginOption;
	external?: ExternalOption;
	resolve?: {
		/**
		* > [!WARNING]
		* > `resolve.alias` will not call `resolveId` hooks of other plugin.
		* > If you want to call `resolveId` hooks of other plugin, use `aliasPlugin` from `rolldown/experimental` instead.
		* > You could find more discussion in [this issue](https://github.com/rolldown/rolldown/issues/3615)
		*/
		alias?: Record<string, string[] | string>
		aliasFields?: string[][]
		conditionNames?: string[]
		/**
		* Map of extensions to alternative extensions.
		*
		* With writing `import './foo.js'` in a file, you want to resolve it to `foo.ts` instead of `foo.js`.
		* You can achieve this by setting: `extensionAlias: { '.js': ['.ts', '.js'] }`.
		*/
		extensionAlias?: Record<string, string[]>
		exportsFields?: string[][]
		extensions?: string[]
		mainFields?: string[]
		mainFiles?: string[]
		modules?: string[]
		symlinks?: boolean
		tsconfigFilename?: string
	};
	cwd?: string;
	/**
	* Expected platform where the code run.
	*
	* @default
	* - 'node' if the format is 'cjs'
	* - 'browser' for other formats
	*/
	platform?: "node" | "browser" | "neutral";
	shimMissingExports?: boolean;
	treeshake?: boolean | TreeshakingOptions;
	logLevel?: LogLevelOption;
	onLog?: (level: LogLevel, log: RollupLog, defaultHandler: LogOrStringHandler) => void;
	onwarn?: (warning: RollupLog, defaultHandler: (warning: RollupLogWithString | (() => RollupLogWithString)) => void) => void;
	moduleTypes?: ModuleTypes;
	experimental?: {
		enableComposingJsPlugins?: boolean
		strictExecutionOrder?: boolean
		disableLiveBindings?: boolean
		viteMode?: boolean
		resolveNewUrlToAsset?: boolean
		hmr?: HmrOptions
	};
	/**
	* Replace global variables or [property accessors](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Property_accessors) with the provided values.
	*
	* # Examples
	*
	* - Replace the global variable `IS_PROD` with `true`
	*
	* ```js rolldown.config.js
	* export default defineConfig({ define: { IS_PROD: 'true' // or JSON.stringify(true) } })
	* ```
	*
	* Result:
	*
	* ```js
	* // Input
	* if (IS_PROD) {
	*   console.log('Production mode')
	* }
	*
	* // After bundling
	* if (true) {
	*   console.log('Production mode')
	* }
	* ```
	*
	* - Replace the property accessor `process.env.NODE_ENV` with `'production'`
	*
	* ```js rolldown.config.js
	* export default defineConfig({ define: { 'process.env.NODE_ENV': "'production'" } })
	* ```
	*
	* Result:
	*
	* ```js
	* // Input
	* if (process.env.NODE_ENV === 'production') {
	*  console.log('Production mode')
	* }
	*
	* // After bundling
	* if ('production' === 'production') {
	* console.log('Production mode')
	* }
	*
	* ```
	*/
	define?: Record<string, string>;
	/**
	* Inject import statements on demand.
	*
	* ## Supported patterns
	* ```js
	* {
	*   // import { Promise } from 'es6-promise'
	*   Promise: ['es6-promise', 'Promise'],
	*
	*   // import { Promise as P } from 'es6-promise'
	*   P: ['es6-promise', 'Promise'],
	*
	*   // import $ from 'jquery'
	*   $: 'jquery',
	*
	*   // import * as fs from 'node:fs'
	*   fs: ['node:fs', '*'],
	*
	*   // Inject shims for property access pattern
	*   'Object.assign': path.resolve( 'src/helpers/object-assign.js' ),
	* }
	* ```
	*/
	inject?: Record<string, string | [string, string]>;
	profilerNames?: boolean;
	/**
	* - `false` disables the JSX parser, resulting in a syntax error if JSX syntax is used.
	* - `"preserve"` disables the JSX transformer, preserving the original JSX syntax in the output.
	* - `"react"` enables the `classic` JSX transformer.
	* - `"react-jsx"` enables the `automatic` JSX transformer.
	*
	* @default mode = "automatic"
	*/
	jsx?: false | "react" | "react-jsx" | "preserve" | JsxOptions;
	transform?: OxcTransformOption;
	watch?: WatcherOptions | false;
	dropLabels?: string[];
	keepNames?: boolean;
	checks?: ChecksOptions;
	makeAbsoluteExternalsRelative?: MakeAbsoluteExternalsRelative;
	debug?: {
		sessionId?: string
	};
}

//#endregion
export { AddonFunction, And, AsyncPluginHooks, BuildOptions, BuiltinPlugin, ChunkFileNamesFunction, ConfigExport, CustomPluginOptions, DefineParallelPluginResult, EmittedAsset, EmittedFile, ExistingRawSourceMap, ExternalOption, FilterExpression, FilterExpressionKind, FunctionPluginHooks, GeneralHookFilter, GetModuleInfo, GlobalsFunction, HookFilter, HookFilterExtension, ImportKind, InputOption, InputOptions, InternalModuleFormat, JsxOptions, LoadResult, LogLevel, LogLevelOption, LogOrStringHandler, LoggingFunction, MaybePromise, MinifyOptions, MinimalPluginContext, ModuleFormat, ModuleInfo, ModuleOptions, ModuleType, ModuleTypeFilter, NormalizedInputOptions, NormalizedOutputOptions, ObjectHook, OutputAsset, OutputBundle, OutputChunk, OutputOptions, ParallelPluginHooks, PartialNull, PartialResolvedId, Plugin, PluginContext, PluginContextMeta, PreRenderedAsset, RenderedChunk, RenderedModule, ResolveIdExtraOptions, ResolveIdResult, ResolvedId, RolldownBuild, RolldownOptions, RolldownOutput, RolldownPlugin, RolldownPluginOption, RolldownWatcher, RolldownWatcherEvent, RollupError, RollupLog, RollupLogWithString, SourceDescription, SourceMap, SourceMapInput, SourcemapIgnoreListOption, TopLevelFilterExpression, TransformPluginContext, TransformResult, TreeshakingOptions, VERSION, WarningHandlerWithDefault, WatchOptions, WatcherOptions, and, build, buildImportAnalysisPlugin, code, defineConfig, defineParallelPlugin, dynamicImportVarsPlugin, exclude, id, importGlobPlugin, include, isolatedDeclarationPlugin, jsonPlugin, loadFallbackPlugin, manifestPlugin, moduleFederationPlugin, modulePreloadPolyfillPlugin, moduleType, not, or, reportPlugin, rolldown, viteResolvePlugin, wasmFallbackPlugin, wasmHelperPlugin, watch, withFilter };