import { __commonJS } from "./shared/chunk-DUYDk_2O.mjs";
import { PluginContextData, bindingifyPlugin, init_bindingify_plugin, init_plugin_context_data } from "./shared/src-DN7it0tm.mjs";
import { import_binding } from "./shared/parse-ast-index-CyY2RRRT.mjs";
import "./shared/filter-index-hnEzlqRW.mjs";
import { parentPort, workerData } from "node:worker_threads";

//#region src/parallel-plugin-worker.ts
var require_parallel_plugin_worker = __commonJS({ "src/parallel-plugin-worker.ts"() {
	init_bindingify_plugin();
	init_plugin_context_data();
	const { registryId, pluginInfos, threadNumber } = workerData;
	(async () => {
		try {
			const plugins = await Promise.all(pluginInfos.map(async (pluginInfo) => {
				const pluginModule = await import(pluginInfo.fileUrl);
				const definePluginImpl = pluginModule.default;
				const plugin = await definePluginImpl(pluginInfo.options, { threadNumber });
				return {
					index: pluginInfo.index,
					plugin: bindingifyPlugin(
						plugin,
						{},
						{},
						// TODO need to find a way to share pluginContextData
						new PluginContextData(),
						[],
						() => {},
						"info",
						// TODO: support this.meta.watchMode
						false
)
				};
			}));
			(0, import_binding.registerPlugins)(registryId, plugins);
			parentPort.postMessage({ type: "success" });
		} catch (error) {
			parentPort.postMessage({
				type: "error",
				error
			});
		} finally {
			parentPort.unref();
		}
	})();
} });

//#endregion
export default require_parallel_plugin_worker();
