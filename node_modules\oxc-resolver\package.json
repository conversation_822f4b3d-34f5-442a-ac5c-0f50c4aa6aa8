{"name": "oxc-resolver", "version": "7.0.1", "license": "MIT", "description": "Oxc Resolver Node API", "packageManager": "pnpm@10.7.1", "homepage": "https://oxc.rs", "repository": {"type": "git", "url": "git+https://github.com/oxc-project/oxc-resolver.git"}, "funding": {"url": "https://github.com/sponsors/Boshen"}, "main": "index.js", "browser": "browser.js", "files": ["index.d.ts", "index.js", "browser.js", "webcontainer-fallback.js"], "scripts": {"test": "vitest run -r ./napi", "build": "napi build --platform --release --manifest-path napi/Cargo.toml", "build:debug": "napi build --platform --manifest-path napi/Cargo.toml", "postbuild": "node napi/patch.mjs"}, "devDependencies": {"@napi-rs/cli": "3.0.0-alpha.77", "@napi-rs/wasm-runtime": "^0.2.7", "@types/node": "^22.13.9", "emnapi": "^1.3.1", "typescript": "^5.8.2", "vitest": "^3.0.8"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "napi": {"binaryName": "resolver", "packageName": "@oxc-resolver/binding", "wasm": {"browser": {"fs": true}}, "targets": ["x86_64-pc-windows-msvc", "aarch64-pc-windows-msvc", "x86_64-unknown-linux-gnu", "x86_64-unknown-linux-musl", "x86_64-unknown-freebsd", "aarch64-unknown-linux-gnu", "aarch64-unknown-linux-musl", "armv7-unknown-linux-gnueabihf", "s390x-unknown-linux-gnu", "riscv64gc-unknown-linux-gnu", "x86_64-apple-darwin", "aarch64-apple-darwin", "wasm32-wasip1-threads"]}, "optionalDependencies": {"@oxc-resolver/binding-win32-x64-msvc": "7.0.1", "@oxc-resolver/binding-win32-arm64-msvc": "7.0.1", "@oxc-resolver/binding-linux-x64-gnu": "7.0.1", "@oxc-resolver/binding-linux-x64-musl": "7.0.1", "@oxc-resolver/binding-freebsd-x64": "7.0.1", "@oxc-resolver/binding-linux-arm64-gnu": "7.0.1", "@oxc-resolver/binding-linux-arm64-musl": "7.0.1", "@oxc-resolver/binding-linux-arm-gnueabihf": "7.0.1", "@oxc-resolver/binding-linux-s390x-gnu": "7.0.1", "@oxc-resolver/binding-linux-riscv64-gnu": "7.0.1", "@oxc-resolver/binding-darwin-x64": "7.0.1", "@oxc-resolver/binding-darwin-arm64": "7.0.1", "@oxc-resolver/binding-wasm32-wasi": "7.0.1"}}