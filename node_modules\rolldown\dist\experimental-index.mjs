import { BuiltinPlugin, buildImportAnalysisPlugin, composeJsPlugins, createBundler, dynamicImportVarsPlugin, handleOutputErrors, importGlobPlugin, init_compose_js_plugins, init_constructors, init_create_bundler, init_normalize_string_or_regex, init_transform_to_rollup_output, isolatedDeclarationPlugin, jsonPlugin, loadFallbackPlugin, manifestPlugin, moduleFederationPlugin, modulePreloadPolyfillPlugin, normalizedStringOrRegex, reportPlugin, viteResolvePlugin, wasmFallbackPlugin, wasmHelperPlugin } from "./shared/src-DN7it0tm.mjs";
import { import_binding } from "./shared/parse-ast-index-CyY2RRRT.mjs";
import "./shared/filter-index-hnEzlqRW.mjs";
import { pathToFileURL } from "node:url";

//#region src/api/experimental.ts
init_create_bundler();
init_transform_to_rollup_output();
/**
* This is an experimental API. It's behavior may change in the future.
*
* Calling this API will only execute the scan stage of rolldown.
*/
const experimental_scan = async (input) => {
	const { bundler, stopWorkers } = await createBundler(input, {});
	const output = await bundler.scan();
	handleOutputErrors(output);
	await stopWorkers?.();
};

//#endregion
//#region src/plugin/parallel-plugin.ts
function defineParallelPlugin(pluginPath) {
	return (options) => {
		return { _parallel: {
			fileUrl: pathToFileURL(pluginPath).href,
			options
		} };
	};
}

//#endregion
//#region src/builtin-plugin/alias-plugin.ts
init_constructors();
function aliasPlugin(config) {
	return new BuiltinPlugin("builtin:alias", config);
}

//#endregion
//#region src/builtin-plugin/replace-plugin.ts
init_constructors();
/**
* Replaces targeted strings in files while bundling.
*
* @example
* // Basic usage
* ```js
* replacePlugin({
*   'process.env.NODE_ENV': JSON.stringify('production'),
*    __buildDate__: () => JSON.stringify(new Date()),
*    __buildVersion: 15
* })
* ```
* @example
* // With options
* ```js
* replacePlugin({
*   'process.env.NODE_ENV': JSON.stringify('production'),
*   __buildDate__: () => JSON.stringify(new Date()),
*   __buildVersion: 15
* }, {
*   preventAssignment: false,
* })
* ```
*/
function replacePlugin(values = {}, options = {}) {
	return new BuiltinPlugin("builtin:replace", {
		...options,
		values
	});
}

//#endregion
//#region src/builtin-plugin/transform-plugin.ts
init_constructors();
init_normalize_string_or_regex();
function transformPlugin(config) {
	if (config) config = {
		...config,
		include: normalizedStringOrRegex(config.include),
		exclude: normalizedStringOrRegex(config.exclude),
		jsxRefreshInclude: normalizedStringOrRegex(config.jsxRefreshInclude),
		jsxRefreshExclude: normalizedStringOrRegex(config.jsxRefreshExclude)
	};
	return new BuiltinPlugin("builtin:transform", config);
}

//#endregion
//#region src/experimental-index.ts
init_compose_js_plugins();
init_constructors();

//#endregion
var isolatedDeclaration = import_binding.isolatedDeclaration;
var moduleRunnerTransform = import_binding.moduleRunnerTransform;
var transform = import_binding.transform;
export { aliasPlugin, buildImportAnalysisPlugin, composeJsPlugins as composePlugins, defineParallelPlugin, dynamicImportVarsPlugin, importGlobPlugin, isolatedDeclaration, isolatedDeclarationPlugin, jsonPlugin, loadFallbackPlugin, manifestPlugin, moduleFederationPlugin, modulePreloadPolyfillPlugin, moduleRunnerTransform, replacePlugin, reportPlugin, experimental_scan as scan, transform, transformPlugin, viteResolvePlugin, wasmFallbackPlugin, wasmHelperPlugin };