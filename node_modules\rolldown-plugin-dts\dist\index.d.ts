import { TsConfigJson } from "get-tsconfig";
import { IsolatedDeclarationsOptions } from "rolldown/experimental";
import { Plugin } from "rolldown";

//#region src/fake-js.d.ts
declare function createFakeJsPlugin({
  dtsInput,
  sourcemap
}: Pick<OptionsResolved, "dtsInput" | "sourcemap">): Plugin;

//#endregion
//#region src/generate.d.ts
declare function createGeneratePlugin({
  compilerOptions,
  isolatedDeclarations,
  emitDtsOnly
}: Pick<OptionsResolved, "compilerOptions" | "isolatedDeclarations" | "emitDtsOnly">): Plugin;

//#endregion
//#region src/index.d.ts
interface Options {
  /**
  * The directory where the the plugin will look for the `tsconfig.json` file.
  */
  cwd?: string;
  /**
  * When entries are `.d.ts` files (instead of `.ts` files), this option should be set to `true`.
  *
  * If enabled, the plugin will skip generating a `.d.ts` file for the entry point.
  */
  dtsInput?: boolean;
  /**
  * When `true`, the plugin will only emit `.d.ts` files and remove all other chunks.
  *
  * This feature is particularly beneficial when you need to generate `d.ts` files for the CommonJS format as part of a separate build process.
  */
  emitDtsOnly?: boolean;
  /**
  * The path to the `tsconfig.json` file.
  *
  * When set to `false`, the plugin will ignore any `tsconfig.json` file.
  * However, `compilerOptions` can still be specified directly in the options.
  *
  * @default `tsconfig.json`
  */
  tsconfig?: string | boolean;
  /**
  * The `compilerOptions` for the TypeScript compiler.
  *
  * @see https://www.typescriptlang.org/docs/handbook/compiler-options.html
  */
  compilerOptions?: TsConfigJson.CompilerOptions;
  /**
  * When `true`, the plugin will generate `.d.ts` files using Oxc,
  * which is blazingly faster than `typescript` compiler.
  *
  * This option is enabled when `isolatedDeclarations` in `compilerOptions` is set to `true`.
  */
  isolatedDeclarations?: boolean | Omit<IsolatedDeclarationsOptions, "sourcemap">;
  /**
  * When `true`, the plugin will generate declaration maps for `.d.ts` files.
  */
  sourcemap?: boolean;
  /** Resolve external types used in dts files from `node_modules` */
  resolve?: boolean | (string | RegExp)[];
}
type Overwrite<T, U> = Pick<T, Exclude<keyof T, keyof U>> & U;
type OptionsResolved = Overwrite<Required<Options>, {
  tsconfig: string | undefined;
  isolatedDeclarations: IsolatedDeclarationsOptions | false;
}>;
declare function dts(options?: Options): Plugin[];
declare function resolveOptions({
  cwd,
  tsconfig,
  compilerOptions,
  isolatedDeclarations,
  sourcemap,
  dtsInput,
  emitDtsOnly,
  resolve
}: Options): OptionsResolved;

//#endregion
export { Options, OptionsResolved, createFakeJsPlugin, createGeneratePlugin, dts, resolveOptions };