import type { AppointmentResultsRes, ExamCenterListRes, ExamCenterOptionsListRes, ExamSessionListRes, UploadFileRes } from "./type";
type BaseResponse = {
    data: string;
    code: string;
};
declare function getCookie({ province }: {
    province: string;
}): Promise<string>;
declare function getCaptchaPre({ cookie, province, }: {
    cookie: string;
    province: string;
}): Promise<unknown>;
declare function getCaptcha({ seed, cookie, province, }: {
    seed: string;
    cookie: string;
    province: string;
}): Promise<BaseResponse>;
declare function verifyCaptcha({ x, y, cookie, province, }: {
    x: string;
    y: string;
    cookie: string;
    province: string;
}): Promise<BaseResponse>;
declare function getExamCenterList({ token, cookie, startDate, endDate, province, }: {
    token: string;
    cookie: string;
    startDate: string;
    endDate: string;
    province: string;
}): Promise<ExamCenterListRes>;
declare function getExamCenterOptionsList({ cookie, fzjg, kskm, province, }: {
    cookie: string;
    fzjg: string;
    kskm: string;
    province: string;
}): Promise<ExamCenterOptionsListRes>;
declare function getExamSessionList({ cookie, ksdd, kskm, fzjg, startDate, endDate, province, }: {
    cookie: string;
    ksdd: string;
    kskm: string;
    fzjg: string;
    startDate: string;
    endDate: string;
    province: string;
}): Promise<ExamSessionListRes>;
declare function getAppointmentResults({ cookie, xh, province, }: {
    cookie: string;
    xh: string;
    province: string;
}): Promise<AppointmentResultsRes>;
declare function uploadFile({ file, filename, permission, strategy_id, }: {
    file: Buffer;
    filename: string;
    permission?: string;
    strategy_id?: string;
}): Promise<UploadFileRes>;
declare function sendMessage({ pushKey, imageUrl, }: {
    pushKey: string;
    imageUrl: string;
}): void;
export { getCookie, getCaptchaPre, getCaptcha, verifyCaptcha, getExamCenterList, getExamCenterOptionsList, getExamSessionList, getAppointmentResults, uploadFile, sendMessage, };
