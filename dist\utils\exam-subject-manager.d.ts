import { VehicleType, ExamSubject, VehicleCategory } from '../types/exam-subject.js';
/**
 * 科目分类管理器
 * 处理不同车型的科目分类和验证逻辑
 */
export declare class ExamSubjectManager {
    /**
     * 获取配置中启用的科目列表，根据车型过滤
     */
    static getEnabledSubjectsForConfig(configSubjects: string[], enabledVehicleTypes: VehicleType[]): ExamSubject[];
    /**
     * 根据车型类别分组科目
     */
    static groupSubjectsByCategory(subjects: ExamSubject[]): Record<VehicleCategory, ExamSubject[]>;
    /**
     * 检查某个科目是否属于指定车型类别
     */
    static isSubjectForCategory(subject: ExamSubject, category: VehicleCategory): boolean;
    /**
     * 验证考生数据的车型和科目组合是否有效
     */
    static validateExamineeData(examCarType: string, examSubject: string): {
        isValid: boolean;
        error?: string;
        normalizedVehicleType?: VehicleType;
        normalizedSubject?: ExamSubject;
    };
    /**
     * 标准化车型字符串到枚举
     */
    static normalizeVehicleType(vehicleTypeStr: string): VehicleType | null;
    /**
     * 标准化科目字符串到枚举
     */
    static normalizeSubject(subjectStr: string): ExamSubject | null;
    /**
     * 获取科目的采集配置
     * 包括是否需要处理科目8、9的特殊情况
     */
    static getSubjectCollectionConfig(configSubjects: string[]): {
        standardSubjects: ExamSubject[];
        hasExtendedSubjects: boolean;
        extendedSubjects: string[];
    };
    /**
     * 处理科目8、9的数据归类
     * 根据代码中的逻辑，科目8、9的数据应该归类到标准科目中
     */
    static normalizeExtendedSubject(extendedSubject: string, sessionData: any): ExamSubject | null;
    /**
     * 生成考试统计报告
     */
    static generateExamReport(examData: any[]): {
        byCategory: Record<VehicleCategory, Record<ExamSubject, number>>;
        byVehicleType: Record<VehicleType, Record<ExamSubject, number>>;
        invalidRecords: any[];
    };
}
export default ExamSubjectManager;
