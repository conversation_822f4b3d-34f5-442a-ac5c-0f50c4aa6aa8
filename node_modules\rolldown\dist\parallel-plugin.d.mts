import "./shared/binding.d-BuFBNbOu.mjs";
import { MaybePromise, Plugin } from "./shared/input-options.d-f0UzQSvZ.mjs";

//#region src/plugin/parallel-plugin-implementation.d.ts
type ParallelPluginImplementation = Plugin;
type Context = {
	/**
	* Thread number
	*/
	threadNumber: number
};
declare function defineParallelPluginImplementation<Options>(plugin: (Options: Options, context: Context) => MaybePromise<ParallelPluginImplementation>): (Options: Options, context: Context) => MaybePromise<ParallelPluginImplementation>;

//#endregion
export { Context, ParallelPluginImplementation, defineParallelPluginImplementation };