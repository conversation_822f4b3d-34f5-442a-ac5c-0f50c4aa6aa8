import {
  VehicleType,
  ExamSubject,
  VehicleCategory,
  getExamSubjectsForVehicle,
  isSubjectRequiredForVehicle,
  getVehicleCategory,
  getVehicleTypesByCategory,
  isValidSubjectVehicleCombination,
  SUBJECT_SHORT_NAMES,
  VEHICLE_TYPE_NAMES
} from '../types/exam-subject.js';

/**
 * 科目分类管理器
 * 处理不同车型的科目分类和验证逻辑
 */
export class ExamSubjectManager {
  /**
   * 获取配置中启用的科目列表，根据车型过滤
   */
  static getEnabledSubjectsForConfig(
    configSubjects: string[],
    enabledVehicleTypes: VehicleType[]
  ): ExamSubject[] {
    const allRequiredSubjects = new Set<ExamSubject>();
    
    // 收集所有启用车型需要的科目
    enabledVehicleTypes.forEach(vehicleType => {
      const subjects = getExamSubjectsForVehicle(vehicleType);
      subjects.forEach(subject => allRequiredSubjects.add(subject));
    });
    
    // 过滤配置中的科目，只保留有效的
    return configSubjects
      .filter(subject => Object.values(ExamSubject).includes(subject as ExamSubject))
      .map(subject => subject as ExamSubject)
      .filter(subject => allRequiredSubjects.has(subject));
  }

  /**
   * 根据车型类别分组科目
   */
  static groupSubjectsByCategory(subjects: ExamSubject[]): Record<VehicleCategory, ExamSubject[]> {
    const result: Record<VehicleCategory, ExamSubject[]> = {
      [VehicleCategory.CAR]: [],
      [VehicleCategory.MOTORCYCLE]: []
    };

    subjects.forEach(subject => {
      // 检查哪些车型类别需要这个科目
      if (this.isSubjectForCategory(subject, VehicleCategory.CAR)) {
        result[VehicleCategory.CAR].push(subject);
      }
      if (this.isSubjectForCategory(subject, VehicleCategory.MOTORCYCLE)) {
        result[VehicleCategory.MOTORCYCLE].push(subject);
      }
    });

    return result;
  }

  /**
   * 检查某个科目是否属于指定车型类别
   */
  static isSubjectForCategory(subject: ExamSubject, category: VehicleCategory): boolean {
    const vehicleTypes = getVehicleTypesByCategory(category);
    return vehicleTypes.some(vehicleType => 
      isSubjectRequiredForVehicle(vehicleType, subject)
    );
  }

  /**
   * 验证考生数据的车型和科目组合是否有效
   */
  static validateExamineeData(examCarType: string, examSubject: string): {
    isValid: boolean;
    error?: string;
    normalizedVehicleType?: VehicleType;
    normalizedSubject?: ExamSubject;
  } {
    // 标准化车型
    const normalizedVehicleType = this.normalizeVehicleType(examCarType);
    if (!normalizedVehicleType) {
      return {
        isValid: false,
        error: `无效的车型: ${examCarType}`
      };
    }

    // 标准化科目
    const normalizedSubject = this.normalizeSubject(examSubject);
    if (!normalizedSubject) {
      return {
        isValid: false,
        error: `无效的科目: ${examSubject}`
      };
    }

    // 验证组合是否有效
    const isValid = isValidSubjectVehicleCombination(normalizedSubject, normalizedVehicleType);
    if (!isValid) {
      return {
        isValid: false,
        error: `车型 ${VEHICLE_TYPE_NAMES[normalizedVehicleType]} 不需要考 ${SUBJECT_SHORT_NAMES[normalizedSubject]}`
      };
    }

    return {
      isValid: true,
      normalizedVehicleType,
      normalizedSubject
    };
  }

  /**
   * 标准化车型字符串到枚举
   */
  static normalizeVehicleType(vehicleTypeStr: string): VehicleType | null {
    if (!vehicleTypeStr) return null;
    
    const upperStr = vehicleTypeStr.toUpperCase().trim();
    
    // 直接匹配
    if (Object.values(VehicleType).includes(upperStr as VehicleType)) {
      return upperStr as VehicleType;
    }
    
    // 处理混合车型，如 "C1,C2" 或 "C1&C2"
    if (upperStr.includes(',') || upperStr.includes('&') || upperStr.includes('、')) {
      const types = upperStr.split(/[,&、]/).map(t => t.trim());
      // 返回第一个有效的车型
      for (const type of types) {
        if (Object.values(VehicleType).includes(type as VehicleType)) {
          return type as VehicleType;
        }
      }
    }
    
    // 模糊匹配
    for (const vehicleType of Object.values(VehicleType)) {
      if (upperStr.includes(vehicleType)) {
        return vehicleType;
      }
    }
    
    return null;
  }

  /**
   * 标准化科目字符串到枚举
   */
  static normalizeSubject(subjectStr: string): ExamSubject | null {
    if (!subjectStr) return null;
    
    const trimmed = subjectStr.trim();
    
    // 直接匹配数字
    if (Object.values(ExamSubject).includes(trimmed as ExamSubject)) {
      return trimmed as ExamSubject;
    }
    
    // 提取数字
    const match = trimmed.match(/(\d+)/);
    if (match) {
      const number = match[1];
      if (Object.values(ExamSubject).includes(number as ExamSubject)) {
        return number as ExamSubject;
      }
    }
    
    return null;
  }

  /**
   * 获取科目的采集配置
   * 包括是否需要处理科目8、9的特殊情况
   */
  static getSubjectCollectionConfig(configSubjects: string[]): {
    standardSubjects: ExamSubject[];
    hasExtendedSubjects: boolean;
    extendedSubjects: string[];
  } {
    const standardSubjects: ExamSubject[] = [];
    const extendedSubjects: string[] = [];
    
    configSubjects.forEach(subject => {
      const trimmed = subject.trim();
      if (Object.values(ExamSubject).includes(trimmed as ExamSubject)) {
        standardSubjects.push(trimmed as ExamSubject);
      } else {
        // 科目8、9等扩展科目
        extendedSubjects.push(trimmed);
      }
    });
    
    return {
      standardSubjects,
      hasExtendedSubjects: extendedSubjects.length > 0,
      extendedSubjects
    };
  }

  /**
   * 处理科目8、9的数据归类
   * 根据代码中的逻辑，科目8、9的数据应该归类到标准科目中
   */
  static normalizeExtendedSubject(
    extendedSubject: string, 
    sessionData: any
  ): ExamSubject | null {
    // 优先使用场次中的实际科目
    if (sessionData && sessionData.kskm) {
      const actualSubject = this.normalizeSubject(sessionData.kskm.toString());
      if (actualSubject) {
        return actualSubject;
      }
    }
    
    // 根据扩展科目的规则进行映射
    // 这里可以根据具体业务规则进行扩展
    switch (extendedSubject) {
      case "8":
        // 科目8可能对应科目1或2，需要根据具体业务确定
        return ExamSubject.SUBJECT_1;
      case "9":
        // 科目9可能对应科目3或4，需要根据具体业务确定
        return ExamSubject.SUBJECT_3;
      default:
        return null;
    }
  }

  /**
   * 生成考试统计报告
   */
  static generateExamReport(examData: any[]): {
    byCategory: Record<VehicleCategory, Record<ExamSubject, number>>;
    byVehicleType: Record<VehicleType, Record<ExamSubject, number>>;
    invalidRecords: any[];
  } {
    const byCategory: Record<VehicleCategory, Record<ExamSubject, number>> = {
      [VehicleCategory.CAR]: {
        [ExamSubject.SUBJECT_1]: 0,
        [ExamSubject.SUBJECT_2]: 0,
        [ExamSubject.SUBJECT_3]: 0,
        [ExamSubject.SUBJECT_4]: 0
      },
      [VehicleCategory.MOTORCYCLE]: {
        [ExamSubject.SUBJECT_1]: 0,
        [ExamSubject.SUBJECT_2]: 0,
        [ExamSubject.SUBJECT_3]: 0,
        [ExamSubject.SUBJECT_4]: 0
      }
    };
    
    const byVehicleType: Record<VehicleType, Record<ExamSubject, number>> = {} as any;
    const invalidRecords: any[] = [];

    // 初始化统计结构
    Object.values(VehicleType).forEach(vehicleType => {
      byVehicleType[vehicleType] = {
        [ExamSubject.SUBJECT_1]: 0,
        [ExamSubject.SUBJECT_2]: 0,
        [ExamSubject.SUBJECT_3]: 0,
        [ExamSubject.SUBJECT_4]: 0
      };
      const subjects = getExamSubjectsForVehicle(vehicleType);
      subjects.forEach(subject => {
        byVehicleType[vehicleType][subject] = 0;
      });
    });

    Object.values(ExamSubject).forEach(subject => {
      byCategory[VehicleCategory.CAR][subject] = 0;
      byCategory[VehicleCategory.MOTORCYCLE][subject] = 0;
    });

    // 统计数据
    examData.forEach(record => {
      const validation = this.validateExamineeData(
        record.exam_car_type, 
        record.exam_subject
      );
      
      if (!validation.isValid || !validation.normalizedVehicleType || !validation.normalizedSubject) {
        invalidRecords.push({
          ...record,
          error: validation.error
        });
        return;
      }

      const vehicleType = validation.normalizedVehicleType;
      const subject = validation.normalizedSubject;
      const category = getVehicleCategory(vehicleType);

      // 增加计数
      byVehicleType[vehicleType][subject]++;
      byCategory[category][subject]++;
    });

    return {
      byCategory,
      byVehicleType,
      invalidRecords
    };
  }
}

export default ExamSubjectManager;