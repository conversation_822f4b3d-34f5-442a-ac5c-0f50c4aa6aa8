/**
 * 驾驶证考试科目类型定义
 * 根据实际情况：
 * - 摩托车（D、E、F证）：只有科目二和科目三
 * - 小型汽车（C1、C2、C5证）：有科目一到科目四
 */

// 车型枚举
export enum VehicleType {
  // 小型汽车类
  C1 = "C1",   // 小型汽车
  C2 = "C2",   // 小型自动挡汽车
  C5 = "C5",   // 残疾人专用小型自动挡载客汽车
  
  // 摩托车类
  D = "D",     // 普通三轮摩托车
  E = "E",     // 普通二轮摩托车
  F = "F",     // 轻便摩托车
}

// 科目枚举
export enum ExamSubject {
  SUBJECT_1 = "1",  // 科目一：道路交通安全法律、法规和相关知识考试
  SUBJECT_2 = "2",  // 科目二：场地驾驶技能考试
  SUBJECT_3 = "3",  // 科目三：道路驾驶技能考试
  SUBJECT_4 = "4",  // 科目四：安全文明驾驶常识考试
}

// 车型分类
export enum VehicleCategory {
  CAR = "CAR",           // 汽车类
  MOTORCYCLE = "MOTORCYCLE"  // 摩托车类
}

// 车型与类别的映射
export const VEHICLE_CATEGORY_MAP: Record<VehicleType, VehicleCategory> = {
  [VehicleType.C1]: VehicleCategory.CAR,
  [VehicleType.C2]: VehicleCategory.CAR,
  [VehicleType.C5]: VehicleCategory.CAR,
  [VehicleType.D]: VehicleCategory.MOTORCYCLE,
  [VehicleType.E]: VehicleCategory.MOTORCYCLE,
  [VehicleType.F]: VehicleCategory.MOTORCYCLE,
};

// 车型对应的考试科目
export const VEHICLE_EXAM_SUBJECTS: Record<VehicleType, ExamSubject[]> = {
  // 小型汽车类：科目1-4
  [VehicleType.C1]: [ExamSubject.SUBJECT_1, ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3, ExamSubject.SUBJECT_4],
  [VehicleType.C2]: [ExamSubject.SUBJECT_1, ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3, ExamSubject.SUBJECT_4],
  [VehicleType.C5]: [ExamSubject.SUBJECT_1, ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3, ExamSubject.SUBJECT_4],
  
  // 摩托车类：只有科目2和3
  [VehicleType.D]: [ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3],
  [VehicleType.E]: [ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3],
  [VehicleType.F]: [ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3],
};

// 按车型类别分组的科目
export const CATEGORY_EXAM_SUBJECTS: Record<VehicleCategory, ExamSubject[]> = {
  [VehicleCategory.CAR]: [ExamSubject.SUBJECT_1, ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3, ExamSubject.SUBJECT_4],
  [VehicleCategory.MOTORCYCLE]: [ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3],
};

// 科目名称映射
export const SUBJECT_NAMES: Record<ExamSubject, string> = {
  [ExamSubject.SUBJECT_1]: "科目一（道路交通安全法律、法规和相关知识考试）",
  [ExamSubject.SUBJECT_2]: "科目二（场地驾驶技能考试）",
  [ExamSubject.SUBJECT_3]: "科目三（道路驾驶技能考试）",
  [ExamSubject.SUBJECT_4]: "科目四（安全文明驾驶常识考试）",
};

// 科目简称映射
export const SUBJECT_SHORT_NAMES: Record<ExamSubject, string> = {
  [ExamSubject.SUBJECT_1]: "科目一",
  [ExamSubject.SUBJECT_2]: "科目二", 
  [ExamSubject.SUBJECT_3]: "科目三",
  [ExamSubject.SUBJECT_4]: "科目四",
};

// 车型名称映射
export const VEHICLE_TYPE_NAMES: Record<VehicleType, string> = {
  [VehicleType.C1]: "C1（小型汽车）",
  [VehicleType.C2]: "C2（小型自动挡汽车）",
  [VehicleType.C5]: "C5（残疾人专用小型自动挡载客汽车）",
  [VehicleType.D]: "D（普通三轮摩托车）",
  [VehicleType.E]: "E（普通二轮摩托车）",
  [VehicleType.F]: "F（轻便摩托车）",
};

/**
 * 工具函数：获取指定车型的考试科目
 */
export function getExamSubjectsForVehicle(vehicleType: VehicleType): ExamSubject[] {
  return VEHICLE_EXAM_SUBJECTS[vehicleType] || [];
}

/**
 * 工具函数：判断指定车型是否需要考某个科目
 */
export function isSubjectRequiredForVehicle(vehicleType: VehicleType, subject: ExamSubject): boolean {
  return getExamSubjectsForVehicle(vehicleType).includes(subject);
}

/**
 * 工具函数：获取车型类别
 */
export function getVehicleCategory(vehicleType: VehicleType): VehicleCategory {
  return VEHICLE_CATEGORY_MAP[vehicleType];
}

/**
 * 工具函数：根据车型类别获取所有车型
 */
export function getVehicleTypesByCategory(category: VehicleCategory): VehicleType[] {
  return Object.entries(VEHICLE_CATEGORY_MAP)
    .filter(([, cat]) => cat === category)
    .map(([vehicleType]) => vehicleType as VehicleType);
}

/**
 * 工具函数：验证科目和车型的组合是否有效
 */
export function isValidSubjectVehicleCombination(subject: ExamSubject, vehicleType: VehicleType): boolean {
  return isSubjectRequiredForVehicle(vehicleType, subject);
}

/**
 * 扩展的考生数据类型，基于原有结构
 */
export interface ExamineeRecord {
  id?: number;
  name: string;                    // 姓名
  id_number: string;               // 身份证号
  allowed_car_type: VehicleType;   // 准考车型（使用枚举）
  appointment_result: string;      // 预约结果
  sort_time: string;              // 排序时间
  exam_date: string;              // 考试日期
  exam_desc: string;              // 考试描述
  exam_car_type: VehicleType;     // 考试车型（使用枚举）
  exam_venue: string;             // 考试场地
  detailed_address: string;       // 详细地址
  exam_subject: ExamSubject;      // 考试科目（使用枚举）
  created_at: string;             // 创建时间
}

/**
 * 考试配置类型
 */
export interface ExamConfig {
  // 支持的车型列表
  supportedVehicleTypes: VehicleType[];
  
  // 支持的科目列表（会根据车型自动过滤）
  enabledSubjects: ExamSubject[];
  
  // 是否启用摩托车考试
  enableMotorcycleExams: boolean;
  
  // 是否启用汽车考试
  enableCarExams: boolean;
}

/**
 * 统计数据的车型映射
 */
export interface SubjectStatsByVehicle {
  [VehicleType.C1]: number;
  [VehicleType.C2]: number;
  [VehicleType.C5]: number;
  [VehicleType.D]?: number;
  [VehicleType.E]?: number;
  [VehicleType.F]?: number;
}