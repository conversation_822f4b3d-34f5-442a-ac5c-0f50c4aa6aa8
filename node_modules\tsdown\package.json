{"name": "tsdown", "version": "0.11.1", "description": "The Elegant Bundler for Libraries", "type": "module", "license": "MIT", "homepage": "https://github.com/rolldown/tsdown#readme", "bugs": {"url": "https://github.com/rolldown/tsdown/issues"}, "repository": {"type": "git", "url": "git+https://github.com/rolldown/tsdown.git"}, "author": "三咲智子 <PERSON> <<EMAIL>>", "funding": "https://github.com/sponsors/sxzz", "files": ["dist", "esm-shims.js"], "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./config": "./dist/config.js", "./plugins": "./dist/plugins.js", "./package.json": "./package.json"}, "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "bin": {"tsdown": "./dist/run.js"}, "publishConfig": {"access": "public"}, "peerDependencies": {"publint": "^0.3.0", "unplugin-lightningcss": "^0.3.3", "unplugin-unused": "^0.4.0"}, "peerDependenciesMeta": {"publint": {"optional": true}, "unplugin-lightningcss": {"optional": true}, "unplugin-unused": {"optional": true}}, "dependencies": {"ansis": "^3.17.0", "cac": "^6.7.14", "chokidar": "^4.0.3", "debug": "^4.4.0", "diff": "^7.0.0", "empathic": "^1.1.0", "hookable": "^5.5.3", "rolldown": "1.0.0-beta.8-commit.534fde3", "rolldown-plugin-dts": "^0.11.1", "semver": "^7.7.1", "tinyexec": "^1.0.1", "tinyglobby": "^0.2.13", "unconfig": "^7.3.2"}, "devDependencies": {"@oxc-node/core": "^0.0.27", "@sxzz/eslint-config": "^6.2.0", "@sxzz/prettier-config": "^2.2.1", "@sxzz/test-utils": "^0.5.6", "@types/debug": "^4.1.12", "@types/diff": "^7.0.2", "@types/node": "^22.15.14", "@types/semver": "^7.7.0", "@unocss/eslint-plugin": "^66.1.0", "bumpp": "^10.1.0", "eslint": "^9.26.0", "lightningcss": "^1.29.3", "pkg-types": "^2.1.0", "prettier": "^3.5.3", "publint": "^0.3.12", "tsup": "^8.4.0", "typedoc": "^0.28.4", "typedoc-plugin-markdown": "^4.6.3", "typescript": "~5.8.3", "unocss": "^66.1.0", "unplugin-lightningcss": "^0.3.3", "unplugin-unused": "^0.4.4", "vite": "^6.3.5", "vitepress": "^1.6.3", "vitepress-plugin-group-icons": "^1.5.2", "vitepress-plugin-llms": "^1.1.3", "vitest": "^3.1.3", "vue": "^3.5.13"}, "engines": {"node": ">=18.0.0"}, "prettier": "@sxzz/prettier-config", "scripts": {"lint": "eslint --cache .", "lint:fix": "pnpm run lint --fix", "build": "node --import @oxc-node/core/register ./src/run.ts", "dev": "node --import @oxc-node/core/register ./src/run.ts", "test": "vitest", "typecheck": "tsc --noEmit", "format": "prettier --cache --write .", "release": "bumpp && pnpm publish", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "docs:generate": "./docs/.vitepress/scripts/docs-generate.sh"}}