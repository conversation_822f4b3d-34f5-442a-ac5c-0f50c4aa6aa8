import { __commonJS, __esm, __require, __toESM } from "./chunk-DUYDk_2O.mjs";

//#region src/webcontainer-fallback.js
var require_webcontainer_fallback = __commonJS({ "src/webcontainer-fallback.js"(exports, module) {
	const fs = __require("node:fs");
	const childProcess = __require("node:child_process");
	const rolldownPkg = JSON.parse(fs.readFileSync(__require.resolve("rolldown/package.json"), "utf-8"));
	const version = rolldownPkg.version;
	const baseDir = `/tmp/rolldown-${version}`;
	const bindingEntry = `${baseDir}/node_modules/@rolldown/binding-wasm32-wasi/rolldown-binding.wasi.cjs`;
	if (!fs.existsSync(bindingEntry)) {
		const bindingPkg = `@rolldown/binding-wasm32-wasi@${version}`;
		fs.rmSync(baseDir, {
			recursive: true,
			force: true
		});
		fs.mkdirSync(baseDir, { recursive: true });
		console.log(`[rolldown] Downloading ${bindingPkg} on WebContainer...`);
		childProcess.execFileSync("pnpm", ["i", bindingPkg], {
			cwd: baseDir,
			stdio: "inherit"
		});
	}
	module.exports = __require(bindingEntry);
} });

//#endregion
//#region src/binding.js
var require_binding = __commonJS({ "src/binding.js"(exports, module) {
	const { createRequire } = __require("node:module");
	const { readFileSync } = __require("node:fs");
	let nativeBinding = null;
	const loadErrors = [];
	const isMusl = () => {
		let musl = false;
		if (process.platform === "linux") {
			musl = isMuslFromFilesystem();
			if (musl === null) musl = isMuslFromReport();
			if (musl === null) musl = isMuslFromChildProcess();
		}
		return musl;
	};
	const isFileMusl = (f) => f.includes("libc.musl-") || f.includes("ld-musl-");
	const isMuslFromFilesystem = () => {
		try {
			return readFileSync("/usr/bin/ldd", "utf-8").includes("musl");
		} catch {
			return null;
		}
	};
	const isMuslFromReport = () => {
		let report = null;
		if (typeof process.report?.getReport === "function") {
			process.report.excludeNetwork = true;
			report = process.report.getReport();
		}
		if (!report) return null;
		if (report.header && report.header.glibcVersionRuntime) return false;
		if (Array.isArray(report.sharedObjects)) {
			if (report.sharedObjects.some(isFileMusl)) return true;
		}
		return false;
	};
	const isMuslFromChildProcess = () => {
		try {
			return __require("child_process").execSync("ldd --version", { encoding: "utf8" }).includes("musl");
		} catch (e) {
			return false;
		}
	};
	function requireNative() {
		if (process.env.NAPI_RS_NATIVE_LIBRARY_PATH) try {
			nativeBinding = __require(process.env.NAPI_RS_NATIVE_LIBRARY_PATH);
		} catch (err) {
			loadErrors.push(err);
		}
		else if (process.platform === "android") if (process.arch === "arm64") {
			try {
				return __require("../rolldown-binding.android-arm64.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-android-arm64");
			} catch (e) {
				loadErrors.push(e);
			}
		} else if (process.arch === "arm") {
			try {
				return __require("../rolldown-binding.android-arm-eabi.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-android-arm-eabi");
			} catch (e) {
				loadErrors.push(e);
			}
		} else loadErrors.push(new Error(`Unsupported architecture on Android ${process.arch}`));
		else if (process.platform === "win32") if (process.arch === "x64") {
			try {
				return __require("../rolldown-binding.win32-x64-msvc.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-win32-x64-msvc");
			} catch (e) {
				loadErrors.push(e);
			}
		} else if (process.arch === "ia32") {
			try {
				return __require("../rolldown-binding.win32-ia32-msvc.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-win32-ia32-msvc");
			} catch (e) {
				loadErrors.push(e);
			}
		} else if (process.arch === "arm64") {
			try {
				return __require("../rolldown-binding.win32-arm64-msvc.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-win32-arm64-msvc");
			} catch (e) {
				loadErrors.push(e);
			}
		} else loadErrors.push(new Error(`Unsupported architecture on Windows: ${process.arch}`));
		else if (process.platform === "darwin") {
			try {
				return __require("../rolldown-binding.darwin-universal.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-darwin-universal");
			} catch (e) {
				loadErrors.push(e);
			}
			if (process.arch === "x64") {
				try {
					return __require("../rolldown-binding.darwin-x64.node");
				} catch (e) {
					loadErrors.push(e);
				}
				try {
					return __require("@rolldown/binding-darwin-x64");
				} catch (e) {
					loadErrors.push(e);
				}
			} else if (process.arch === "arm64") {
				try {
					return __require("../rolldown-binding.darwin-arm64.node");
				} catch (e) {
					loadErrors.push(e);
				}
				try {
					return __require("@rolldown/binding-darwin-arm64");
				} catch (e) {
					loadErrors.push(e);
				}
			} else loadErrors.push(new Error(`Unsupported architecture on macOS: ${process.arch}`));
		} else if (process.platform === "freebsd") if (process.arch === "x64") {
			try {
				return __require("../rolldown-binding.freebsd-x64.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-freebsd-x64");
			} catch (e) {
				loadErrors.push(e);
			}
		} else if (process.arch === "arm64") {
			try {
				return __require("../rolldown-binding.freebsd-arm64.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-freebsd-arm64");
			} catch (e) {
				loadErrors.push(e);
			}
		} else loadErrors.push(new Error(`Unsupported architecture on FreeBSD: ${process.arch}`));
		else if (process.platform === "linux") if (process.arch === "x64") if (isMusl()) {
			try {
				return __require("../rolldown-binding.linux-x64-musl.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-linux-x64-musl");
			} catch (e) {
				loadErrors.push(e);
			}
		} else {
			try {
				return __require("../rolldown-binding.linux-x64-gnu.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-linux-x64-gnu");
			} catch (e) {
				loadErrors.push(e);
			}
		}
		else if (process.arch === "arm64") if (isMusl()) {
			try {
				return __require("../rolldown-binding.linux-arm64-musl.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-linux-arm64-musl");
			} catch (e) {
				loadErrors.push(e);
			}
		} else {
			try {
				return __require("../rolldown-binding.linux-arm64-gnu.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-linux-arm64-gnu");
			} catch (e) {
				loadErrors.push(e);
			}
		}
		else if (process.arch === "arm") if (isMusl()) {
			try {
				return __require("../rolldown-binding.linux-arm-musleabihf.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-linux-arm-musleabihf");
			} catch (e) {
				loadErrors.push(e);
			}
		} else {
			try {
				return __require("../rolldown-binding.linux-arm-gnueabihf.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-linux-arm-gnueabihf");
			} catch (e) {
				loadErrors.push(e);
			}
		}
		else if (process.arch === "riscv64") if (isMusl()) {
			try {
				return __require("../rolldown-binding.linux-riscv64-musl.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-linux-riscv64-musl");
			} catch (e) {
				loadErrors.push(e);
			}
		} else {
			try {
				return __require("../rolldown-binding.linux-riscv64-gnu.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-linux-riscv64-gnu");
			} catch (e) {
				loadErrors.push(e);
			}
		}
		else if (process.arch === "ppc64") {
			try {
				return __require("../rolldown-binding.linux-ppc64-gnu.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-linux-ppc64-gnu");
			} catch (e) {
				loadErrors.push(e);
			}
		} else if (process.arch === "s390x") {
			try {
				return __require("../rolldown-binding.linux-s390x-gnu.node");
			} catch (e) {
				loadErrors.push(e);
			}
			try {
				return __require("@rolldown/binding-linux-s390x-gnu");
			} catch (e) {
				loadErrors.push(e);
			}
		} else loadErrors.push(new Error(`Unsupported architecture on Linux: ${process.arch}`));
		else loadErrors.push(new Error(`Unsupported OS: ${process.platform}, architecture: ${process.arch}`));
	}
	nativeBinding = requireNative();
	if (!nativeBinding || process.env.NAPI_RS_FORCE_WASI) {
		try {
			nativeBinding = __require("../rolldown-binding.wasi.cjs");
		} catch (err) {
			if (process.env.NAPI_RS_FORCE_WASI) loadErrors.push(err);
		}
		if (!nativeBinding) try {
			nativeBinding = __require("@rolldown/binding-wasm32-wasi");
		} catch (err) {
			if (process.env.NAPI_RS_FORCE_WASI) loadErrors.push(err);
		}
	}
	if (!nativeBinding && globalThis.process?.versions?.["webcontainer"]) try {
		nativeBinding = require_webcontainer_fallback();
	} catch (err) {
		loadErrors.push(err);
	}
	if (!nativeBinding) {
		if (loadErrors.length > 0) throw new Error("Failed to load native binding", { cause: loadErrors });
		throw new Error(`Failed to load native binding`);
	}
	module.exports.BindingBundleEndEventData = nativeBinding.BindingBundleEndEventData;
	module.exports.BindingCallableBuiltinPlugin = nativeBinding.BindingCallableBuiltinPlugin;
	module.exports.BindingError = nativeBinding.BindingError;
	module.exports.BindingModuleInfo = nativeBinding.BindingModuleInfo;
	module.exports.BindingNormalizedOptions = nativeBinding.BindingNormalizedOptions;
	module.exports.BindingOutputAsset = nativeBinding.BindingOutputAsset;
	module.exports.BindingOutputChunk = nativeBinding.BindingOutputChunk;
	module.exports.BindingOutputs = nativeBinding.BindingOutputs;
	module.exports.BindingPluginContext = nativeBinding.BindingPluginContext;
	module.exports.BindingRenderedChunk = nativeBinding.BindingRenderedChunk;
	module.exports.BindingRenderedChunkMeta = nativeBinding.BindingRenderedChunkMeta;
	module.exports.BindingRenderedModule = nativeBinding.BindingRenderedModule;
	module.exports.BindingTransformPluginContext = nativeBinding.BindingTransformPluginContext;
	module.exports.BindingWatcher = nativeBinding.BindingWatcher;
	module.exports.BindingWatcherChangeData = nativeBinding.BindingWatcherChangeData;
	module.exports.BindingWatcherEvent = nativeBinding.BindingWatcherEvent;
	module.exports.Bundler = nativeBinding.Bundler;
	module.exports.ParallelJsPluginRegistry = nativeBinding.ParallelJsPluginRegistry;
	module.exports.ParseResult = nativeBinding.ParseResult;
	module.exports.BindingBuiltinPluginName = nativeBinding.BindingBuiltinPluginName;
	module.exports.BindingHookSideEffects = nativeBinding.BindingHookSideEffects;
	module.exports.BindingLogLevel = nativeBinding.BindingLogLevel;
	module.exports.BindingPluginOrder = nativeBinding.BindingPluginOrder;
	module.exports.ExportExportNameKind = nativeBinding.ExportExportNameKind;
	module.exports.ExportImportNameKind = nativeBinding.ExportImportNameKind;
	module.exports.ExportLocalNameKind = nativeBinding.ExportLocalNameKind;
	module.exports.FilterTokenKind = nativeBinding.FilterTokenKind;
	module.exports.getBufferOffset = nativeBinding.getBufferOffset;
	module.exports.HelperMode = nativeBinding.HelperMode;
	module.exports.ImportNameKind = nativeBinding.ImportNameKind;
	module.exports.isolatedDeclaration = nativeBinding.isolatedDeclaration;
	module.exports.moduleRunnerTransform = nativeBinding.moduleRunnerTransform;
	module.exports.parseAsync = nativeBinding.parseAsync;
	module.exports.parseSync = nativeBinding.parseSync;
	module.exports.parseSyncRaw = nativeBinding.parseSyncRaw;
	module.exports.rawTransferSupported = nativeBinding.rawTransferSupported;
	module.exports.registerPlugins = nativeBinding.registerPlugins;
	module.exports.Severity = nativeBinding.Severity;
	module.exports.shutdownAsyncRuntime = nativeBinding.shutdownAsyncRuntime;
	module.exports.startAsyncRuntime = nativeBinding.startAsyncRuntime;
	module.exports.transform = nativeBinding.transform;
} });
var import_binding = __toESM(require_binding());

//#endregion
//#region src/utils/code-frame.ts
function spaces(index) {
	let result = "";
	while (index--) result += " ";
	return result;
}
function tabsToSpaces(value) {
	return value.replace(/^\t+/, (match) => match.split("	").join("  "));
}
function getCodeFrame(source, line, column) {
	let lines = source.split("\n");
	if (line > lines.length) return "";
	const maxLineLength = Math.max(tabsToSpaces(lines[line - 1].slice(0, column)).length + MIN_CHARACTERS_SHOWN_AFTER_LOCATION + ELLIPSIS.length, LINE_TRUNCATE_LENGTH);
	const frameStart = Math.max(0, line - 3);
	let frameEnd = Math.min(line + 2, lines.length);
	lines = lines.slice(frameStart, frameEnd);
	while (!/\S/.test(lines[lines.length - 1])) {
		lines.pop();
		frameEnd -= 1;
	}
	const digits = String(frameEnd).length;
	return lines.map((sourceLine, index) => {
		const isErrorLine = frameStart + index + 1 === line;
		let lineNumber = String(index + frameStart + 1);
		while (lineNumber.length < digits) lineNumber = ` ${lineNumber}`;
		let displayedLine = tabsToSpaces(sourceLine);
		if (displayedLine.length > maxLineLength) displayedLine = `${displayedLine.slice(0, maxLineLength - ELLIPSIS.length)}${ELLIPSIS}`;
		if (isErrorLine) {
			const indicator = spaces(digits + 2 + tabsToSpaces(sourceLine.slice(0, column)).length) + "^";
			return `${lineNumber}: ${displayedLine}\n${indicator}`;
		}
		return `${lineNumber}: ${displayedLine}`;
	}).join("\n");
}
var LINE_TRUNCATE_LENGTH, MIN_CHARACTERS_SHOWN_AFTER_LOCATION, ELLIPSIS;
var init_code_frame = __esm({ "src/utils/code-frame.ts"() {
	LINE_TRUNCATE_LENGTH = 120;
	MIN_CHARACTERS_SHOWN_AFTER_LOCATION = 10;
	ELLIPSIS = "...";
} });

//#endregion
//#region src/log/locate-character/index.js
/** @typedef {import('./types').Location} Location */
/**
* @param {import('./types').Range} range
* @param {number} index
*/
function rangeContains(range, index) {
	return range.start <= index && index < range.end;
}
/**
* @param {string} source
* @param {import('./types').Options} [options]
*/
function getLocator(source, options = {}) {
	const { offsetLine = 0, offsetColumn = 0 } = options;
	let start = 0;
	const ranges = source.split("\n").map((line, i$1) => {
		const end = start + line.length + 1;
		/** @type {import('./types').Range} */
		const range = {
			start,
			end,
			line: i$1
		};
		start = end;
		return range;
	});
	let i = 0;
	/**
	* @param {string | number} search
	* @param {number} [index]
	* @returns {Location | undefined}
	*/
	function locator(search, index) {
		if (typeof search === "string") search = source.indexOf(search, index ?? 0);
		if (search === -1) return void 0;
		let range = ranges[i];
		const d = search >= range.end ? 1 : -1;
		while (range) {
			if (rangeContains(range, search)) return {
				line: offsetLine + range.line,
				column: offsetColumn + search - range.start,
				character: search
			};
			i += d;
			range = ranges[i];
		}
	}
	return locator;
}
/**
* @param {string} source
* @param {string | number} search
* @param {import('./types').Options} [options]
* @returns {Location | undefined}
*/
function locate(source, search, options) {
	return getLocator(source, options)(search, options && options.startIndex);
}
var init_locate_character = __esm({ "src/log/locate-character/index.js"() {} });

//#endregion
//#region src/log/logs.ts
function logParseError(message) {
	return {
		code: PARSE_ERROR,
		message
	};
}
function logInvalidLogPosition(pluginName) {
	return {
		code: INVALID_LOG_POSITION,
		message: `Plugin "${pluginName}" tried to add a file position to a log or warning. This is only supported in the "transform" hook at the moment and will be ignored.`
	};
}
function logInputHookInOutputPlugin(pluginName, hookName) {
	return {
		code: INPUT_HOOK_IN_OUTPUT_PLUGIN,
		message: `The "${hookName}" hook used by the output plugin ${pluginName} is a build time hook and will not be run for that plugin. Either this plugin cannot be used as an output plugin, or it should have an option to configure it as an output plugin.`
	};
}
function logCycleLoading(pluginName, moduleId) {
	return {
		code: CYCLE_LOADING,
		message: `Found the module "${moduleId}" cycle loading at ${pluginName} plugin, it maybe blocking fetching modules.`
	};
}
function logMultiplyNotifyOption() {
	return {
		code: MULTIPLY_NOTIFY_OPTION,
		message: `Found multiply notify option at watch options, using first one to start notify watcher.`
	};
}
function logPluginError(error$1, plugin, { hook, id } = {}) {
	try {
		const code = error$1.code;
		if (!error$1.pluginCode && code != null && (typeof code !== "string" || !code.startsWith("PLUGIN_"))) error$1.pluginCode = code;
		error$1.code = PLUGIN_ERROR;
		error$1.plugin = plugin;
		if (hook) error$1.hook = hook;
		if (id) error$1.id = id;
	} catch (_) {} finally {
		return error$1;
	}
}
function error(base) {
	if (!(base instanceof Error)) {
		base = Object.assign(new Error(base.message), base);
		Object.defineProperty(base, "name", {
			value: "RollupError",
			writable: true
		});
	}
	throw base;
}
function augmentCodeLocation(properties, pos, source, id) {
	if (typeof pos === "object") {
		const { line, column } = pos;
		properties.loc = {
			column,
			file: id,
			line
		};
	} else {
		properties.pos = pos;
		const location = locate(source, pos, { offsetLine: 1 });
		if (!location) return;
		const { line, column } = location;
		properties.loc = {
			column,
			file: id,
			line
		};
	}
	if (properties.frame === void 0) {
		const { line, column } = properties.loc;
		properties.frame = getCodeFrame(source, line, column);
	}
}
var INVALID_LOG_POSITION, PLUGIN_ERROR, INPUT_HOOK_IN_OUTPUT_PLUGIN, CYCLE_LOADING, MULTIPLY_NOTIFY_OPTION, PARSE_ERROR;
var init_logs = __esm({ "src/log/logs.ts"() {
	init_code_frame();
	init_locate_character();
	INVALID_LOG_POSITION = "INVALID_LOG_POSITION", PLUGIN_ERROR = "PLUGIN_ERROR", INPUT_HOOK_IN_OUTPUT_PLUGIN = "INPUT_HOOK_IN_OUTPUT_PLUGIN", CYCLE_LOADING = "CYCLE_LOADING", MULTIPLY_NOTIFY_OPTION = "MULTIPLY_NOTIFY_OPTION", PARSE_ERROR = "PARSE_ERROR";
} });

//#endregion
//#region ../../node_modules/.pnpm/oxc-parser@0.68.1/node_modules/oxc-parser/wrap.mjs
function wrap$1(result) {
	let program, module$1, comments, errors;
	return {
		get program() {
			if (!program) program = jsonParseAst(result.program);
			return program;
		},
		get module() {
			if (!module$1) module$1 = result.module;
			return module$1;
		},
		get comments() {
			if (!comments) comments = result.comments;
			return comments;
		},
		get errors() {
			if (!errors) errors = result.errors;
			return errors;
		}
	};
}
function jsonParseAst(program) {
	return JSON.parse(program, transform);
}
function transform(key, value) {
	if (value === null && key === "value" && Object.hasOwn(this, "type") && this.type === "Literal") {
		if (Object.hasOwn(this, "bigint")) return BigInt(this.bigint);
		if (Object.hasOwn(this, "regex")) {
			const { regex } = this;
			try {
				return RegExp(regex.pattern, regex.flags);
			} catch (_err) {}
		}
	}
	return value;
}
var init_wrap = __esm({ "../../node_modules/.pnpm/oxc-parser@0.68.1/node_modules/oxc-parser/wrap.mjs"() {} });

//#endregion
//#region src/parse-ast-index.ts
function wrap(result, sourceText) {
	result = wrap$1(result);
	if (result.errors.length > 0) return normalizeParseError(sourceText, result.errors);
	return result.program;
}
function normalizeParseError(sourceText, errors) {
	let message = `Parse failed with ${errors.length} error${errors.length < 2 ? "" : "s"}:\n`;
	for (let i = 0; i < errors.length; i++) {
		if (i >= 5) {
			message += "\n...";
			break;
		}
		const e = errors[i];
		message += e.message + "\n" + e.labels.map((label) => {
			const location = locate(sourceText, label.start, { offsetLine: 1 });
			if (!location) return;
			return getCodeFrame(sourceText, location.line, location.column);
		}).filter(Boolean).join("\n");
	}
	return error(logParseError(message));
}
function parseAst(sourceText, options, filename) {
	return wrap((0, import_binding.parseSync)(filename ?? "file.js", sourceText, {
		...defaultParserOptions,
		...options
	}), sourceText);
}
async function parseAstAsync(sourceText, options, filename) {
	return wrap(await (0, import_binding.parseAsync)(filename ?? "file.js", sourceText, {
		...defaultParserOptions,
		...options
	}), sourceText);
}
var defaultParserOptions;
var init_parse_ast_index = __esm({ "src/parse-ast-index.ts"() {
	init_locate_character();
	init_logs();
	init_code_frame();
	init_wrap();
	defaultParserOptions = {
		lang: "js",
		preserveParens: false
	};
} });

//#endregion
export { augmentCodeLocation, error, import_binding, init_logs, init_parse_ast_index, logCycleLoading, logInputHookInOutputPlugin, logInvalidLogPosition, logMultiplyNotifyOption, logPluginError, parseAst, parseAstAsync };