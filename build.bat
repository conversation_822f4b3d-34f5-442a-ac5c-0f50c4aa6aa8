@echo off
REM Windows构建脚本
REM 用于构建驾考预约数据采集系统

echo === 驾考预约数据采集系统 Windows 构建脚本 ===
echo.

REM 检查参数
set BUILD_TARGET=
if "%1"=="--win" set BUILD_TARGET=windows
if "%1"=="--linux" set BUILD_TARGET=linux
if "%1"=="--mac" set BUILD_TARGET=darwin

if "%BUILD_TARGET%"=="" (
    echo 使用方法: build.bat [--win^|--linux^|--mac]
    echo   --win     构建Windows可执行文件
    echo   --linux   构建Linux可执行文件  
    echo   --mac     构建macOS可执行文件
    echo.
    exit /b 1
)

echo 目标平台: %BUILD_TARGET%
echo.

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    exit /b 1
)

echo ✅ Node.js版本:
node --version

REM 检查npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到npm
    exit /b 1
)

echo ✅ npm版本:
npm --version
echo.

REM 安装依赖
echo 📦 安装依赖...
npm install
if errorlevel 1 (
    echo ❌ 依赖安装失败
    exit /b 1
)
echo ✅ 依赖安装完成
echo.

REM 构建TypeScript
echo 🔨 构建TypeScript项目...
npm run build
if errorlevel 1 (
    echo ❌ TypeScript构建失败
    exit /b 1
)
echo ✅ TypeScript构建完成
echo.

REM 修复JSON导入属性
echo 🔧 修复ES模块JSON导入...
powershell -Command "(Get-Content dist\utils\index.js) -replace 'import province from \"\.\.\/constans\/province\.json\";', 'import province from \"../constans/province.json\" with { type: \"json\" };' | Set-Content dist\utils\index.js"
powershell -Command "(Get-Content dist\utils\index.js) -replace 'import license from \"\.\.\/constans\/licenseName\.json\";', 'import license from \"../constans/licenseName.json\" with { type: \"json\" };' | Set-Content dist\utils\index.js"
echo ✅ JSON导入修复完成
echo.

REM 检查SEA支持
echo 🔍 检查Single Executable Application支持...
node --experimental-sea-config --help >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告: 当前Node.js版本可能不支持SEA，将跳过可执行文件构建
    echo ✅ 项目构建完成，可以使用 npm run start 启动
    goto :end
)

REM 创建SEA配置
echo 📝 创建SEA配置文件...
echo { > sea-config.json
echo   "main": "dist/index.js", >> sea-config.json
echo   "output": "sea-prep.blob", >> sea-config.json
echo   "disableExperimentalSEAWarning": true, >> sea-config.json
echo   "useSnapshot": false, >> sea-config.json
echo   "useCodeCache": true >> sea-config.json
echo } >> sea-config.json

REM 生成SEA blob
echo 🔄 生成SEA blob文件...
node --experimental-sea-config sea-config.json
if errorlevel 1 (
    echo ❌ SEA blob生成失败
    exit /b 1
)

REM 复制Node.js可执行文件
echo 📋 复制Node.js可执行文件...
if "%BUILD_TARGET%"=="windows" (
    copy "%NODE_HOME%\node.exe" examinees-collection.exe >nul 2>&1
    if errorlevel 1 (
        where node >temp_node_path.txt
        set /p NODE_PATH=<temp_node_path.txt
        del temp_node_path.txt
        copy "!NODE_PATH!" examinees-collection.exe
    )
    set EXECUTABLE=examinees-collection.exe
) else (
    echo ⚠️  跨平台构建需要对应平台的Node.js二进制文件
    echo 当前仅支持Windows平台构建
    goto :end
)

REM 注入SEA blob
echo 💉 注入SEA blob到可执行文件...
npx postject examinees-collection.exe NODE_SEA_BLOB sea-prep.blob --sentinel-fuse NODE_SEA_FUSE_fce680ab2cc467b6e072b8b5df1996b2
if errorlevel 1 (
    echo ❌ SEA blob注入失败，可能需要安装postject
    echo 尝试安装postject...
    npm install -g postject
    if errorlevel 1 (
        echo ❌ postject安装失败
        exit /b 1
    )
    echo 重新尝试注入...
    npx postject examinees-collection.exe NODE_SEA_BLOB sea-prep.blob --sentinel-fuse NODE_SEA_FUSE_fce680ab2cc467b6e072b8b5df1996b2
    if errorlevel 1 (
        echo ❌ SEA blob注入失败
        exit /b 1
    )
)

REM 清理临时文件
echo 🧹 清理临时文件...
del sea-config.json >nul 2>&1
del sea-prep.blob >nul 2>&1

echo.
echo 🎉 构建完成！
echo.
echo 📁 输出文件: examinees-collection.exe
echo 📊 文件大小:
dir examinees-collection.exe | findstr examinees-collection.exe
echo.
echo 🚀 使用方法:
echo   1. 确保配置文件 app-config.json 存在
echo   2. 运行: examinees-collection.exe
echo.
echo 📋 注意事项:
echo   - 首次运行可能需要较长时间
echo   - 确保MariaDB服务正在运行（如果使用MariaDB模式）
echo   - 可执行文件包含了所有依赖，可以独立运行

:end
echo.
echo ✅ 构建脚本执行完成
