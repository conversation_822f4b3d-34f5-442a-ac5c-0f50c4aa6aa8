import { bgRed, bg<PERSON><PERSON>w, blue, green } from "ansis";

//#region src/utils/logger.ts
var Logger = class {
	silent = false;
	setSilent(value) {
		this.silent = value;
	}
	info(...args) {
		if (!this.silent) console.info(blue`ℹ`, ...args);
	}
	warn(...args) {
		if (!this.silent) console.warn("\n", bgYellow` WARN `, ...args, "\n");
	}
	error(...args) {
		if (!this.silent) console.error("\n", bgRed` ERROR `, ...args, "\n");
	}
	success(...args) {
		if (!this.silent) console.info(green`✔`, ...args);
	}
};
const logger = new Logger();

//#endregion
export { logger };