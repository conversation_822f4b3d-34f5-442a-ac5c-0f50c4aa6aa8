import { BindingReplacePluginConfig, BindingTransformPluginConfig, IsolatedDeclarationsOptions, IsolatedDeclarationsResult, TransformOptions, TransformResult$1 as TransformResult, isolatedDeclaration, moduleRunnerTransform, transform } from "./shared/binding.d-BIqDyMRC.cjs";
import { BuiltinPlugin, InputOptions, RolldownPlugin, buildImportAnalysisPlugin, defineParallelPlugin, dynamicImportVarsPlugin, importGlobPlugin, isolatedDeclarationPlugin, jsonPlugin, loadFallbackPlugin, manifestPlugin, moduleFederationPlugin, modulePreloadPolyfillPlugin, reportPlugin, viteResolvePlugin, wasmFallbackPlugin, wasmHelperPlugin } from "./shared/input-options.d-7hGLjZwc.cjs";

//#region src/api/experimental.d.ts
/**
* This is an experimental API. It's behavior may change in the future.
*
* Calling this API will only execute the scan stage of rolldown.
*/
/**
* This is an experimental API. It's behavior may change in the future.
*
* Calling this API will only execute the scan stage of rolldown.
*/
declare const experimental_scan: (input: InputOptions) => Promise<void>;

//#endregion
//#region src/utils/compose-js-plugins.d.ts
declare function composeJsPlugins(plugins: RolldownPlugin[]): RolldownPlugin[];

//#endregion
//#region src/builtin-plugin/alias-plugin.d.ts
type AliasPluginAlias = {
	find: string | RegExp
	replacement: string
};
type AliasPluginConfig = {
	entries: AliasPluginAlias[]
};
declare function aliasPlugin(config: AliasPluginConfig): BuiltinPlugin;

//#endregion
//#region src/builtin-plugin/replace-plugin.d.ts
/**
* Replaces targeted strings in files while bundling.
*
* @example
* // Basic usage
* ```js
* replacePlugin({
*   'process.env.NODE_ENV': JSON.stringify('production'),
*    __buildDate__: () => JSON.stringify(new Date()),
*    __buildVersion: 15
* })
* ```
* @example
* // With options
* ```js
* replacePlugin({
*   'process.env.NODE_ENV': JSON.stringify('production'),
*   __buildDate__: () => JSON.stringify(new Date()),
*   __buildVersion: 15
* }, {
*   preventAssignment: false,
* })
* ```
*/
/**
* Replaces targeted strings in files while bundling.
*
* @example
* // Basic usage
* ```js
* replacePlugin({
*   'process.env.NODE_ENV': JSON.stringify('production'),
*    __buildDate__: () => JSON.stringify(new Date()),
*    __buildVersion: 15
* })
* ```
* @example
* // With options
* ```js
* replacePlugin({
*   'process.env.NODE_ENV': JSON.stringify('production'),
*   __buildDate__: () => JSON.stringify(new Date()),
*   __buildVersion: 15
* }, {
*   preventAssignment: false,
* })
* ```
*/
declare function replacePlugin(values?: BindingReplacePluginConfig["values"], options?: Omit<BindingReplacePluginConfig, "values">): BuiltinPlugin;

//#endregion
//#region src/builtin-plugin/transform-plugin.d.ts
type TransformPattern = string | RegExp | readonly (RegExp | string)[];
type TransformPluginConfig = Omit<BindingTransformPluginConfig, "include" | "exclude" | "jsxRefreshInclude" | "jsxRefreshExclude"> & {
	include?: TransformPattern
	exclude?: TransformPattern
	jsxRefreshInclude?: TransformPattern
	jsxRefreshExclude?: TransformPattern
};
declare function transformPlugin(config?: TransformPluginConfig): BuiltinPlugin;

//#endregion
export { IsolatedDeclarationsOptions, IsolatedDeclarationsResult, TransformOptions, TransformResult, aliasPlugin, buildImportAnalysisPlugin, composeJsPlugins as composePlugins, defineParallelPlugin, dynamicImportVarsPlugin, importGlobPlugin, isolatedDeclaration, isolatedDeclarationPlugin, jsonPlugin, loadFallbackPlugin, manifestPlugin, moduleFederationPlugin, modulePreloadPolyfillPlugin, moduleRunnerTransform, replacePlugin, reportPlugin, experimental_scan as scan, transform, transformPlugin, viteResolvePlugin, wasmFallbackPlugin, wasmHelperPlugin };