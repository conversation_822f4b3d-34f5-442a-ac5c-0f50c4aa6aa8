/**
 * 使用示例：集成新的驾驶证考试科目分类系统
 *
 * 这个文件展示了如何在现有代码中使用新的科目分类逻辑
 */
import ExamSubjectManager from './utils/exam-subject-manager.js';
import { VehicleType, ExamSubject, VehicleCategory, ExamineeRecord } from './types/exam-subject.js';
/**
 * 处理考生数据的增强版本
 * 集成了新的科目分类验证
 */
function enhancedSaveExamineeToDatabase(examinee, examInfo, db) {
    // 验证车型和科目组合
    const validation = ExamSubjectManager.validateExamineeData(examInfo.examCarType, examInfo.examSubject);
    if (!validation.isValid) {
        console.warn(`数据验证失败: ${validation.error}`, {
            examinee: examinee.xm,
            carType: examInfo.examCarType,
            subject: examInfo.examSubject
        });
        return { success: false, error: validation.error };
    }
    // 使用验证后的标准化数据
    const normalizedData = {
        name: examinee.xm || "",
        id_number: examinee.sfzmhm || "",
        allowed_car_type: validation.normalizedVehicleType,
        appointment_result: examinee.zt || "",
        sort_time: examinee.pxsjStr ? new Date(examinee.pxsjStr).toISOString() : "",
        exam_date: examInfo.examDate,
        exam_desc: examInfo.examDesc,
        exam_car_type: validation.normalizedVehicleType,
        exam_venue: examInfo.examVenue,
        detailed_address: examInfo.detailedAddress,
        exam_subject: validation.normalizedSubject,
        created_at: new Date().toISOString(),
    };
    // 保存到数据库
    db.insertExaminee(normalizedData);
    return { success: true };
}
/**
 * 增强的科目数据采集函数
 * 支持扩展科目的处理
 */
async function enhancedCollectExamData(cookie, provinceCode, fzjg, db, config) {
    // 获取科目采集配置
    const collectionConfig = ExamSubjectManager.getSubjectCollectionConfig(config.subjects);
    console.log('科目采集配置:', {
        标准科目: collectionConfig.standardSubjects,
        扩展科目: collectionConfig.extendedSubjects,
        有扩展科目: collectionConfig.hasExtendedSubjects
    });
    // 采集标准科目
    for (const subject of collectionConfig.standardSubjects) {
        console.log(`正在采集标准科目 ${subject}...`);
        await collectSubjectData(cookie, provinceCode, fzjg, db, subject);
    }
    // 采集扩展科目（如科目8、9）
    for (const extendedSubject of collectionConfig.extendedSubjects) {
        console.log(`正在采集扩展科目 ${extendedSubject}...`);
        await collectExtendedSubjectData(cookie, provinceCode, fzjg, db, extendedSubject);
    }
}
/**
 * 标准科目数据采集
 */
async function collectSubjectData(cookie, provinceCode, fzjg, db, subject) {
    // 这里是原有的采集逻辑，但会使用新的验证方法
    // ... 原有代码逻辑
    console.log(`采集科目 ${subject} 完成`);
}
/**
 * 扩展科目数据采集
 * 处理科目8、9等特殊情况
 */
async function collectExtendedSubjectData(cookie, provinceCode, fzjg, db, extendedSubject) {
    console.log(`开始采集扩展科目 ${extendedSubject}`);
    // 获取扩展科目的考场列表
    // const examCenterList = await getExamCenterOptionsList({...});
    // 模拟处理考试场次数据
    // for (const session of examSessionList) {
    //   // 使用扩展科目归一化逻辑
    //   const normalizedSubject = ExamSubjectManager.normalizeExtendedSubject(
    //     extendedSubject, 
    //     session
    //   );
    //   
    //   if (normalizedSubject) {
    //     // 处理数据时使用归一化后的科目
    //     // processExamSession(session, examCenter, cookie, provinceCode, normalizedSubject, db);
    //   }
    // }
    console.log(`扩展科目 ${extendedSubject} 采集完成`);
}
/**
 * 生成增强的统计报告
 */
async function generateEnhancedReport(db) {
    // 获取所有考生数据
    const allExaminees = []; // 从数据库获取
    // 生成分类统计报告
    const report = ExamSubjectManager.generateExamReport(allExaminees);
    console.log('\n========== 增强统计报告 ==========');
    // 按车型类别统计
    console.log('\n按车型类别统计:');
    Object.entries(report.byCategory).forEach(([category, stats]) => {
        console.log(`${category === 'CAR' ? '汽车类' : '摩托车类'}:`);
        Object.entries(stats).forEach(([subject, count]) => {
            if (count > 0) {
                console.log(`  科目${subject}: ${count}人`);
            }
        });
    });
    // 按具体车型统计
    console.log('\n按车型统计:');
    Object.entries(report.byVehicleType).forEach(([vehicleType, stats]) => {
        const totalCount = Object.values(stats).reduce((sum, count) => sum + count, 0);
        if (totalCount > 0) {
            console.log(`${vehicleType}:`);
            Object.entries(stats).forEach(([subject, count]) => {
                if (count > 0) {
                    console.log(`  科目${subject}: ${count}人`);
                }
            });
        }
    });
    // 无效记录
    if (report.invalidRecords.length > 0) {
        console.log(`\n无效记录: ${report.invalidRecords.length}条`);
        report.invalidRecords.slice(0, 5).forEach(record => {
            console.log(`  ${record.name} - ${record.error}`);
        });
    }
}
/**
 * 配置验证示例
 */
function validateConfiguration(config) {
    console.log('\n========== 配置验证 ==========');
    // 验证车型配置
    const invalidVehicleTypes = config.enabledVehicleTypes.filter((type) => !Object.values(VehicleType).includes(type));
    if (invalidVehicleTypes.length > 0) {
        console.error(`无效的车型配置: ${invalidVehicleTypes.join(', ')}`);
        return false;
    }
    // 验证科目配置
    const collectionConfig = ExamSubjectManager.getSubjectCollectionConfig(config.subjects);
    console.log(`标准科目: ${collectionConfig.standardSubjects.join(', ')}`);
    if (collectionConfig.hasExtendedSubjects) {
        console.log(`扩展科目: ${collectionConfig.extendedSubjects.join(', ')}`);
    }
    // 验证车型与科目的兼容性
    const enabledSubjects = ExamSubjectManager.getEnabledSubjectsForConfig(config.subjects, config.enabledVehicleTypes);
    console.log(`根据车型过滤后的有效科目: ${enabledSubjects.join(', ')}`);
    return true;
}
export { enhancedSaveExamineeToDatabase, enhancedCollectExamData, generateEnhancedReport, validateConfiguration };
//# sourceMappingURL=enhanced-exam-collection.js.map