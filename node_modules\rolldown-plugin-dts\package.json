{"name": "rolldown-plugin-dts", "version": "0.11.1", "description": "A Rolldown plugin to bundle dts files", "type": "module", "license": "MIT", "homepage": "https://github.com/sxzz/rolldown-plugin-dts#readme", "bugs": {"url": "https://github.com/sxzz/rolldown-plugin-dts/issues"}, "repository": {"type": "git", "url": "git+https://github.com/sxzz/rolldown-plugin-dts.git"}, "author": "三咲智子 <PERSON> <<EMAIL>>", "funding": "https://github.com/sponsors/sxzz", "files": ["dist"], "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "peerDependencies": {"rolldown": "^1.0.0-beta.8-commit.534fde3", "typescript": "^5.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"@babel/generator": "^7.27.1", "@babel/parser": "^7.27.1", "@babel/types": "^7.27.1", "ast-kit": "^1.4.3", "debug": "^4.4.0", "dts-resolver": "^1.1.0", "get-tsconfig": "^4.10.0"}, "devDependencies": {"@sxzz/eslint-config": "^6.2.0", "@sxzz/prettier-config": "^2.2.1", "@sxzz/test-utils": "^0.5.6", "@types/babel__generator": "^7.27.0", "@types/debug": "^4.1.12", "@types/diff": "^7.0.2", "@types/node": "^22.15.3", "bumpp": "^10.1.0", "diff": "^7.0.0", "eslint": "^9.25.1", "estree-walker": "^3.0.3", "prettier": "^3.5.3", "rolldown": "^1.0.0-beta.8-commit.534fde3", "rollup-plugin-dts": "^6.2.1", "tinyglobby": "^0.2.13", "tsdown": "^0.10.2", "tsx": "^4.19.4", "typescript": "^5.8.3", "vitest": "^3.1.2"}, "engines": {"node": ">=20.18.0"}, "resolutions": {"rolldown": "^1.0.0-beta.8-commit.534fde3", "rolldown-plugin-dts": "workspace:*"}, "prettier": "@sxzz/prettier-config", "scripts": {"lint": "eslint --cache .", "lint:fix": "pnpm run lint --fix", "build": "tsdown", "dev": "tsdown --watch", "test": "vitest", "typecheck": "tsc --noEmit", "format": "prettier --cache --write .", "release": "bumpp && pnpm publish"}}