import { __esm } from "./chunk-DUYDk_2O.mjs";

//#region src/utils/misc.ts
function arraify(value) {
	return Array.isArray(value) ? value : [value];
}
function isNullish(value) {
	return value === null || value === void 0;
}
function isPromiseLike(value) {
	return value && (typeof value === "object" || typeof value === "function") && typeof value.then === "function";
}
function unimplemented(info) {
	if (info) throw new Error(`unimplemented: ${info}`);
	throw new Error("unimplemented");
}
function unreachable(info) {
	if (info) throw new Error(`unreachable: ${info}`);
	throw new Error("unreachable");
}
function unsupported(info) {
	throw new Error(`UNSUPPORTED: ${info}`);
}
function noop(..._args) {}
var init_misc = __esm({ "src/utils/misc.ts"() {} });

//#endregion
//#region src/plugin/with-filter.ts
function withFilterImpl(pluginOption, filterObjectList) {
	if (isPromiseLike(pluginOption)) return pluginOption.then((p) => withFilter(p, filterObjectList));
	if (pluginOption == false || pluginOption == null) return pluginOption;
	if (Array.isArray(pluginOption)) return pluginOption.map((p) => withFilter(p, filterObjectList));
	let plugin = pluginOption;
	let filterObjectIndex = findMatchedFilterObject(plugin.name, filterObjectList);
	if (filterObjectIndex === -1) return plugin;
	let filterObject = filterObjectList[filterObjectIndex];
	Object.keys(plugin).forEach((key) => {
		switch (key) {
			case "transform":
			case "resolveId":
			case "load":
				if (!plugin[key]) return;
				if (typeof plugin[key] === "object") plugin[key].filter = filterObject[key] ?? plugin[key].filter;
				else plugin[key] = {
					handler: plugin[key],
					filter: filterObject[key]
				};
				break;
			default: break;
		}
	});
	return plugin;
}
function withFilter(pluginOption, filterObject) {
	return withFilterImpl(pluginOption, arraify(filterObject));
}
function findMatchedFilterObject(pluginName, overrideFilterObjectList) {
	if (overrideFilterObjectList.length === 1 && overrideFilterObjectList[0].pluginNamePattern === void 0) return 0;
	for (let i = 0; i < overrideFilterObjectList.length; i++) for (let j = 0; j < (overrideFilterObjectList[i].pluginNamePattern ?? []).length; j++) {
		let pattern = overrideFilterObjectList[i].pluginNamePattern[j];
		if (typeof pattern === "string" && pattern === pluginName) return i;
		else if (pattern instanceof RegExp && pattern.test(pluginName)) return i;
	}
	return -1;
}
var init_with_filter = __esm({ "src/plugin/with-filter.ts"() {
	init_misc();
} });

//#endregion
//#region src/plugin/index.ts
var init_plugin = __esm({ "src/plugin/index.ts"() {
	init_with_filter();
} });

//#endregion
//#region src/filter-index.ts
function and(...args) {
	return new And(...args);
}
function or(...args) {
	return new Or(...args);
}
function not(expr) {
	return new Not(expr);
}
function id(pattern) {
	return new Id(pattern);
}
function moduleType(pattern) {
	return new ModuleType(pattern);
}
function code(pattern) {
	return new Code(pattern);
}
function include(expr) {
	return new Include(expr);
}
function exclude(expr) {
	return new Exclude(expr);
}
var And, Or, Not, Id, ModuleType, Code, Include, Exclude;
var init_filter_index = __esm({ "src/filter-index.ts"() {
	init_plugin();
	And = class {
		kind;
		args;
		constructor(...args) {
			if (args.length === 0) throw new Error("`And` expects at least one operand");
			this.args = args;
			this.kind = "and";
		}
	};
	Or = class {
		kind;
		args;
		constructor(...args) {
			if (args.length === 0) throw new Error("`Or` expects at least one operand");
			this.args = args;
			this.kind = "or";
		}
	};
	Not = class {
		kind;
		expr;
		constructor(expr) {
			this.expr = expr;
			this.kind = "not";
		}
	};
	Id = class {
		kind;
		pattern;
		constructor(pattern) {
			this.pattern = pattern;
			this.kind = "id";
		}
	};
	ModuleType = class {
		kind;
		pattern;
		constructor(pattern) {
			this.pattern = pattern;
			this.kind = "moduleType";
		}
	};
	Code = class {
		kind;
		pattern;
		constructor(expr) {
			this.pattern = expr;
			this.kind = "code";
		}
	};
	Include = class {
		kind;
		expr;
		constructor(expr) {
			this.expr = expr;
			this.kind = "include";
		}
	};
	Exclude = class {
		kind;
		expr;
		constructor(expr) {
			this.expr = expr;
			this.kind = "exclude";
		}
	};
} });

//#endregion
export { And, and, arraify, code, exclude, id, include, init_filter_index, init_misc, isNullish, moduleType, noop, not, or, unimplemented, unreachable, unsupported, withFilter };