/**
 * 驾驶证考试科目类型定义
 * 根据实际情况：
 * - 摩托车（D、E、F证）：只有科目二和科目三
 * - 小型汽车（C1、C2、C5证）：有科目一到科目四
 */
// 车型枚举
export var VehicleType;
(function (VehicleType) {
    // 小型汽车类
    VehicleType["C1"] = "C1";
    VehicleType["C2"] = "C2";
    VehicleType["C5"] = "C5";
    // 摩托车类
    VehicleType["D"] = "D";
    VehicleType["E"] = "E";
    VehicleType["F"] = "F";
})(VehicleType || (VehicleType = {}));
// 科目枚举
export var ExamSubject;
(function (ExamSubject) {
    ExamSubject["SUBJECT_1"] = "1";
    ExamSubject["SUBJECT_2"] = "2";
    ExamSubject["SUBJECT_3"] = "3";
    ExamSubject["SUBJECT_4"] = "4";
})(ExamSubject || (ExamSubject = {}));
// 车型分类
export var VehicleCategory;
(function (VehicleCategory) {
    VehicleCategory["CAR"] = "CAR";
    VehicleCategory["MOTORCYCLE"] = "MOTORCYCLE"; // 摩托车类
})(VehicleCategory || (VehicleCategory = {}));
// 车型与类别的映射
export const VEHICLE_CATEGORY_MAP = {
    [VehicleType.C1]: VehicleCategory.CAR,
    [VehicleType.C2]: VehicleCategory.CAR,
    [VehicleType.C5]: VehicleCategory.CAR,
    [VehicleType.D]: VehicleCategory.MOTORCYCLE,
    [VehicleType.E]: VehicleCategory.MOTORCYCLE,
    [VehicleType.F]: VehicleCategory.MOTORCYCLE,
};
// 车型对应的考试科目
export const VEHICLE_EXAM_SUBJECTS = {
    // 小型汽车类：科目1-4
    [VehicleType.C1]: [ExamSubject.SUBJECT_1, ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3, ExamSubject.SUBJECT_4],
    [VehicleType.C2]: [ExamSubject.SUBJECT_1, ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3, ExamSubject.SUBJECT_4],
    [VehicleType.C5]: [ExamSubject.SUBJECT_1, ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3, ExamSubject.SUBJECT_4],
    // 摩托车类：只有科目2和3
    [VehicleType.D]: [ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3],
    [VehicleType.E]: [ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3],
    [VehicleType.F]: [ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3],
};
// 按车型类别分组的科目
export const CATEGORY_EXAM_SUBJECTS = {
    [VehicleCategory.CAR]: [ExamSubject.SUBJECT_1, ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3, ExamSubject.SUBJECT_4],
    [VehicleCategory.MOTORCYCLE]: [ExamSubject.SUBJECT_2, ExamSubject.SUBJECT_3],
};
// 科目名称映射
export const SUBJECT_NAMES = {
    [ExamSubject.SUBJECT_1]: "科目一（道路交通安全法律、法规和相关知识考试）",
    [ExamSubject.SUBJECT_2]: "科目二（场地驾驶技能考试）",
    [ExamSubject.SUBJECT_3]: "科目三（道路驾驶技能考试）",
    [ExamSubject.SUBJECT_4]: "科目四（安全文明驾驶常识考试）",
};
// 科目简称映射
export const SUBJECT_SHORT_NAMES = {
    [ExamSubject.SUBJECT_1]: "科目一",
    [ExamSubject.SUBJECT_2]: "科目二",
    [ExamSubject.SUBJECT_3]: "科目三",
    [ExamSubject.SUBJECT_4]: "科目四",
};
// 车型名称映射
export const VEHICLE_TYPE_NAMES = {
    [VehicleType.C1]: "C1（小型汽车）",
    [VehicleType.C2]: "C2（小型自动挡汽车）",
    [VehicleType.C5]: "C5（残疾人专用小型自动挡载客汽车）",
    [VehicleType.D]: "D（普通三轮摩托车）",
    [VehicleType.E]: "E（普通二轮摩托车）",
    [VehicleType.F]: "F（轻便摩托车）",
};
/**
 * 工具函数：获取指定车型的考试科目
 */
export function getExamSubjectsForVehicle(vehicleType) {
    return VEHICLE_EXAM_SUBJECTS[vehicleType] || [];
}
/**
 * 工具函数：判断指定车型是否需要考某个科目
 */
export function isSubjectRequiredForVehicle(vehicleType, subject) {
    return getExamSubjectsForVehicle(vehicleType).includes(subject);
}
/**
 * 工具函数：获取车型类别
 */
export function getVehicleCategory(vehicleType) {
    return VEHICLE_CATEGORY_MAP[vehicleType];
}
/**
 * 工具函数：根据车型类别获取所有车型
 */
export function getVehicleTypesByCategory(category) {
    return Object.entries(VEHICLE_CATEGORY_MAP)
        .filter(([, cat]) => cat === category)
        .map(([vehicleType]) => vehicleType);
}
/**
 * 工具函数：验证科目和车型的组合是否有效
 */
export function isValidSubjectVehicleCombination(subject, vehicleType) {
    return isSubjectRequiredForVehicle(vehicleType, subject);
}
//# sourceMappingURL=exam-subject.js.map