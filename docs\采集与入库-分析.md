# 采集与数据入库：全链路分析与改进建议

以下内容总结了当前项目中“采集 → 入库 → 统计/产出”的端到端流程、字段映射、幂等策略、写入与性能、异常与时区注意点，以及潜在问题与改进方向，便于后续维护与优化。

---

一、端到端流程概览（采集 → 入库）
- 启动与配置加载
  - 程序入口在主任务中完成初始化与抓取流程（src/index.ts）。
  - 初始化数据库与统计天数，配置从 app-config.json 加载（province、fzjg、days_ahead、statistics_days 等）。
  - 日期范围按“数据库内已存在的最晚考试日期 + 1 天”到“days_ahead 后”的区间动态计算（calculateDateRange）。

- 采集循环（按科目 1-4 → 按考场 → 按场次 → 获取考生清单）
  - 遍历 kskm=1..4，按科目维度拉取考场选项列表，再逐考场抓取场次列表，最后逐场次请求考生信息（collectAllExamData → processExamCenterData → processExamSession）。
  - 场次级别会调用获取考生预约结果，并在本地过滤后（仅保留 zt === "1" 的记录）进行入库。

- 数据入库（逐考生行插入）
  - 每条考生记录会映射为 examinees 表中的一行（saveExamineeToDatabase → database/index.ts: insertExaminee）。
  - 实际 insert 由数据库类封装，并在每次插入后立即落盘（saveDatabase）。

---

二、数据字段映射（外部数据 → 本地表 examinees）
- 表结构（SQLite/SQL.js）
  - id INTEGER PRIMARY KEY AUTOINCREMENT
  - name, id_number, allowed_car_type, appointment_result, sort_time
  - exam_date, exam_desc, exam_car_type, exam_venue, detailed_address
  - exam_subject, created_at

- 字段来源与转换（典型映射）
  - 姓名 name ← examinee.xm
  - 身份证 id_number ← examinee.sfzmhm
  - 准考车型 allowed_car_type ← examinee.kscx
  - 预约结果 appointment_result ← examinee.zt（仅当 zt === "1" 时入库）
  - 排序时间 sort_time ← examinee.pxsjStr（new Date(...).toISOString()）
  - 考试日期 exam_date ← new Date(session.ksrq).toISOString().split("T")[0]
  - 考试描述 exam_desc ← session.ksccmc（如“第一场”）
  - 考试车型 exam_car_type ← session.kscx
  - 考试场地 exam_venue ← session.ksddmc
  - 详细地址 detailed_address ← examCenter.netSysPlacesite.lxdz
  - 考试科目 exam_subject ← kskm（"1" | "2" | "3" | "4"）
  - 创建时间 created_at ← new Date().toISOString()

---

三、日期范围与幂等策略
- 日期范围来源
  - 通过数据库内“已存在的最晚 exam_date”决定下一次采集的 startDate（= 最晚日期 + 1 天），避免重复采集历史天的数据（getLatestExamDate → calculateDateRange）。

- 幂等与重复数据
  - 通过“起始日期 = 已入库最大日期 + 1”规避了重复写入历史日期记录，从宏观上实现“按天追加”的幂等策略。
  - 表中目前没有唯一键或约束，当“同一天内重复启动程序”或“同一日期数据在平台侧发生变更”时，可能出现：
    - 同一人同一天同科目的重复行（若再次采集了同日数据）
    - 无法反映平台侧最新变更（因同日不会被再次采集）

---

四、数据库写入策略与性能
- 当前策略：每插入一条 examinees 记录就执行一次 saveDatabase（导出并写回文件），I/O 频率较高。
- 对于大量数据（多考场、多场次）写入，频繁落盘会带来性能开销。可优化为“批量插入后统一 saveDatabase”，或设置阈值批量落盘（如每 N 条或每 T 秒）。

---

五、异常处理与健壮性
- 采集流程的考场/场次抓取均有 try/catch，失败会记录日志并继续后续项，具备一定容错性（processExamCenterData、processExamSession）。
- 目前未实现：网络重试、超时控制、节流与速率限制；当外部 API 波动或限流时，可能导致当天部分数据缺失。

---

六、时间与时区注意点
- exam_date、sort_time 等通过 new Date(...).toISOString() 生成，为 UTC 日期/时间。
- 若平台返回的是“本地时区语义日期”，UTC 转换可能导致跨日偏移（如东八区凌晨）。
- 建议：对“日期型字段”采用“仅使用 yyyy-MM-dd 字符串（平台时区）”的精确映射，或在转换前明确时区后再做标准化。

---

七、统计与产出（简述）
- 统计查询在内存数据库（SQL.js）上执行，按科目/日期/场次进行聚合，并生成 HTML，供后续渲染为 PNG（printStatistics、saveHtml）。

---

八、潜在问题与改进建议
1) 初始化竞争风险
- ExamineeDatabase 在构造函数中调用了异步初始化，但外部未显式 await，存在“尚未初始化就被调用”的潜在竞态。
- 建议：
  - 使用异步工厂（如 ExamineeDatabase.create(...) 返回 Promise）并在上层 await；或
  - 类内提供 ready Promise（如 this.ready = this.initializeDatabase()），在首次使用前 await db.ready。

2) 幂等与去重
- 增加唯一约束（如 (id_number, exam_date, exam_subject, exam_venue, exam_car_type) 上 UNIQUE 索引）或业务主键哈希，改用 UPSERT（INSERT ... ON CONFLICT DO NOTHING/UPDATE）。
- 若需“同日数据更新”，允许重新拉取“最近 N 天”的数据并按 UPSERT 更新。

3) 批量落盘
- 改为批量插入后再 saveDatabase，或按数量阈值/时间间隔落盘，显著降低 I/O 成本。

4) 错误重试与熔断
- 对网络请求增加重试（指数退避 + 上限）、请求超时与错误分类；对 5xx 类失败设置熔断与延迟恢复。

5) 时区与日期一致性
- 保留平台原始日期字符串副本；对 exam_date 优先采用平台语义 yyyy-MM-dd，而非 UTC-ISO 截断。

6) 索引与查询性能
- 针对统计常用条件添加索引（如 exam_venue, exam_subject, exam_date 组合索引），提升聚合速度（SQL.js 规模有限但仍有收益）。

7) 数据完整性与审计
- 可增加“原始响应快照”的持久化（JSON 存档或条目哈希）用于回溯与差异比对。

8) 采集策略与速率控制
- 并发请求数量与间隔可配置，避免触发目标站点限流。

9) 字段标准化
- 场次名、车型等建议建立映射与校验，避免后续聚合出现“同义不同写”的维度碎片化。

---

九、可代为实施项（可选）
- 将 ExamineeDatabase 改造为异步工厂/ready 语义，消除初始化竞态。
- 为 examinees 增加唯一约束与 UPSERT 策略，并支持“最近 N 天重采 + 覆盖更新”的幂等采集模式。
- 在请求层加入重试/超时/节流中间件，提升采集稳定性。
- 优化入库为批量落盘（按阈值触发），提升性能并减少 I/O。

如需我落地其中任一改进项，请告诉我你的优先级与约束（例如：是否允许修改表结构、是否需要向后兼容现有统计逻辑、性能与时效哪个优先）。